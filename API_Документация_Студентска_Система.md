# API Документация - Студентска Система за Управление
## Comprehensive техническа документация на български език

---

## 📋 **СЪДЪРЖАНИЕ**

1. [Общ преглед на API архитектурата](#общ-преглед)
2. [Пълен каталог на API endpoints](#каталог-endpoints)
3. [Техническа архитектура на server.js](#техническа-архитектура)
4. [Проектни решения и технологии](#проектни-решения)
5. [Code examples и implementation details](#code-examples)

---

## 🏗️ **ОБЩ ПРЕГЛЕД НА API АРХИТЕКТУРАТА** {#общ-преглед}

### **Архитектурен модел:**
Студентската система за управление използва **RESTful API архитектура** базирана на **4-tier модел**:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client Layer  │───▶│ Application     │───▶│   Data Layer    │    │  Storage Layer  │
│   (Browser)     │    │ Layer (Express) │    │   (MongoDB)     │    │  (Cloudinary)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Ключови характеристики:**
- **40+ REST endpoints** категоризирани по роли
- **JWT-based authentication** с HttpOnly cookies
- **Role-based access control** (student/teacher)
- **Multer + Cloudinary** file upload pipeline
- **MongoDB** за structured data, **Cloudinary** за files
- **Real-time notifications** с polling mechanism

### **Technology Stack:**
- **Runtime:** Node.js v18+
- **Framework:** Express.js v5.1.0
- **Database:** MongoDB Atlas (NoSQL)
- **Authentication:** JWT + bcryptjs
- **File Storage:** Cloudinary CDN
- **Upload Handling:** Multer + Streamifier

---

## 📊 **ПЪЛЕН КАТАЛОГ НА API ENDPOINTS** {#каталог-endpoints}

### **Общи Endpoints (8 броя) - Достъпни за всички роли**

#### **1. POST /register**
- **Описание:** Регистрация на нов потребител
- **Authentication:** Не се изисква
- **Request Body:**
```json
{
  "firstName": "string",
  "lastName": "string", 
  "email": "string",
  "nickname": "string",
  "password": "string",
  "role": "student" // default
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

#### **2. POST /login**
- **Описание:** Вход в системата
- **Authentication:** Не се изисква
- **Request Body:**
```json
{
  "email": "string",
  "password": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string",
  "user": {
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "role": "student|teacher"
  }
}
```

#### **3. POST /logout**
- **Описание:** Изход от системата
- **Authentication:** Не се изисква
- **Request Body:** Празно
- **Response:**
```json
{
  "success": true,
  "message": "Излязохте успешно."
}
```

#### **4. GET /me**
- **Описание:** Получаване на данни за текущия потребител
- **Authentication:** JWT token в cookies
- **Request Body:** Не се изисква
- **Response:**
```json
{
  "success": true/false,
  "user": {
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "avatarUrl": "string",
    "nickname": "string",
    "role": "student|teacher"
  }
}
```

#### **5. POST /upload-avatar**
- **Описание:** Качване на аватар
- **Authentication:** authenticateToken middleware
- **Content-Type:** multipart/form-data
- **Request Body:**
```
avatar: File (image)
```
- **Response:**
```json
{
  "success": true/false,
  "url": "string", // Cloudinary URL
  "message": "string"
}
```

#### **6. POST /update-avatar**
- **Описание:** Обновяване на URL на аватар
- **Authentication:** authenticateToken middleware
- **Request Body:**
```json
{
  "avatarUrl": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

#### **7. POST /get-notifications**
- **Описание:** Получаване на известия за потребителя
- **Authentication:** authenticateToken middleware
- **Request Body:** Празно
- **Response:**
```json
{
  "success": true/false,
  "notifications": [
    {
      "_id": "string",
      "type": "string",
      "title": "string", 
      "message": "string",
      "courseName": "string",
      "isRead": boolean,
      "isUrgent": boolean,
      "createdAt": "date"
    }
  ]
}
```

#### **8. POST /get-user-by-email**
- **Описание:** Получаване на потребител по имейл
- **Authentication:** authenticateToken middleware
- **Request Body:**
```json
{
  "email": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "user": {
    "firstName": "string",
    "lastName": "string",
    "email": "string",
    "avatarUrl": "string"
  }
}
```

### **Студентски Endpoints (15 броя) - Достъпни само за роля 'student'**

#### **9. POST /enroll-course**
- **Описание:** Записване в курс с код
- **Authentication:** authenticateToken + student role
- **Request Body:**
```json
{
  "courseCode": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string",
  "course": {
    "name": "string",
    "description": "string"
  }
}
```

#### **10. POST /get-my-courses**
- **Описание:** Получаване на курсовете на студента
- **Authentication:** authenticateToken + student role
- **Request Body:** Празно
- **Response:**
```json
{
  "success": true/false,
  "courses": [
    {
      "_id": "string",
      "name": "string",
      "description": "string",
      "teacher": "string",
      "unlocked": true
    }
  ]
}
```

#### **11. POST /upload-exercise**
- **Описание:** Качване на упражнение
- **Authentication:** authenticateToken + student role
- **Content-Type:** multipart/form-data
- **Request Body:**
```
exercise: File
courseName: string
exerciseIndex: number
```
- **Response:**
```json
{
  "success": true/false,
  "url": "string", // Cloudinary URL
  "originalName": "string",
  "publicId": "string"
}
```

#### **12. POST /upload-coursework**
- **Описание:** Качване на курсова работа
- **Authentication:** authenticateToken + student role
- **Content-Type:** multipart/form-data
- **Request Body:**
```
coursework: File
courseName: string
```
- **Response:**
```json
{
  "success": true/false,
  "url": "string",
  "originalName": "string",
  "publicId": "string"
}
```

#### **13. POST /get-exercises**
- **Описание:** Получаване на упражнения за курс
- **Authentication:** authenticateToken + student role
- **Request Body:**
```json
{
  "userEmail": "string",
  "courseName": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "exercises": [
    {
      "url": "string",
      "originalName": "string",
      "grade": number // 2-6 scale
    }
  ]
}
```

#### **14. POST /get-attachments**
- **Описание:** Получаване на всички прикачени файлове
- **Authentication:** authenticateToken + student role
- **Request Body:**
```json
{
  "userEmail": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "attachments": {
    "courses": {
      "courseName": {
        "exercises": [],
        "coursework": {}
      }
    }
  }
}
```

#### **15. POST /start-test-session**
- **Описание:** Започване на тест сесия
- **Authentication:** authenticateToken + student role
- **Request Body:**
```json
{
  "testId": "string",
  "courseName": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "test": {
    "_id": "string",
    "title": "string",
    "duration": number,
    "questions": [
      {
        "question": "string",
        "type": "multiple-choice|text|true-false",
        "options": ["string"]
      }
    ]
  },
  "sessionId": "string",
  "timeRemaining": number
}
```

#### **16. POST /submit-test**
- **Описание:** Подаване на отговори за тест
- **Authentication:** authenticateToken + student role
- **Request Body:**
```json
{
  "sessionId": "string",
  "answers": [
    {
      "questionIndex": number,
      "answer": "string"
    }
  ],
  "isAutoSubmit": boolean
}
```
- **Response:**
```json
{
  "success": true/false,
  "score": number,
  "maxScore": number,
  "timeSpent": number,
  "autoSubmitted": boolean
}
```

#### **17. POST /get-test-time-remaining**
- **Описание:** Проверка на оставащо време за тест
- **Authentication:** authenticateToken + student role
- **Request Body:**
```json
{
  "sessionId": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "timeRemaining": number, // seconds
  "expired": boolean
}
```

#### **18. POST /get-course-tests**
- **Описание:** Получаване на тестове за курс
- **Authentication:** authenticateToken + student role
- **Request Body:**
```json
{
  "courseName": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "tests": [
    {
      "_id": "string",
      "title": "string",
      "startDate": "date",
      "endDate": "date",
      "duration": number,
      "status": "scheduled|active|completed|expired"
    }
  ]
}
```

#### **19. POST /get-student-progress**
- **Описание:** Получаване на прогрес на студента
- **Authentication:** authenticateToken + student role
- **Request Body:** Празно
- **Response:**
```json
{
  "success": true/false,
  "progressData": {
    "courses": [
      {
        "courseName": "string",
        "exerciseCount": number,
        "completedExercises": number,
        "courseworkCompleted": boolean,
        "testResults": [],
        "averageGrade": number
      }
    ],
    "totalTasks": number,
    "completedTasks": number,
    "overallProgress": number
  }
}
```

#### **20. POST /validate-test-access**
- **Описание:** Валидиране на достъп до тест
- **Authentication:** authenticateToken + student role
- **Request Body:**
```json
{
  "testId": "string",
  "courseName": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "accessible": boolean,
  "reason": "string",
  "startDate": "date",
  "endDate": "date"
}
```

#### **21. POST /mark-notification-read**
- **Описание:** Маркиране на известие като прочетено
- **Authentication:** authenticateToken + student role
- **Request Body:**
```json
{
  "notificationId": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

#### **22. POST /mark-all-notifications-read**
- **Описание:** Маркиране на всички известия като прочетени
- **Authentication:** authenticateToken + student role
- **Request Body:** Празно
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

#### **23. POST /get-student-notifications**
- **Описание:** Получаване на известия за тестове (студенти)
- **Authentication:** authenticateToken + student role
- **Request Body:** Празно
- **Response:**
```json
{
  "success": true/false,
  "notifications": [
    {
      "type": "test_available|test_deadline|exercise_graded",
      "title": "string",
      "message": "string",
      "courseName": "string",
      "metadata": {
        "dueDate": "date",
        "testId": "string",
        "grade": number
      }
    }
  ]
}
```

### **Учителски Endpoints (17 броя) - Достъпни само за роля 'teacher'**

#### **24. POST /create-course**
- **Описание:** Създаване на нов курс
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "name": "string",
  "description": "string",
  "code": "string",
  "image": "string",
  "teacher": "string",
  "period": "string",
  "category": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string",
  "course": {
    "_id": "string",
    "name": "string",
    "code": "string"
  }
}
```

#### **25. POST /get-all-courses**
- **Описание:** Получаване на всички курсове
- **Authentication:** authenticateToken + teacher role
- **Request Body:** Празно
- **Response:**
```json
{
  "success": true/false,
  "courses": [
    {
      "_id": "string",
      "name": "string",
      "description": "string",
      "teacher": "string",
      "unlocked": boolean
    }
  ]
}
```

#### **26. POST /add-course-content**
- **Описание:** Добавяне на съдържание към курс
- **Authentication:** authenticateToken + teacher role
- **Content-Type:** multipart/form-data
- **Request Body:**
```
content: File
courseName: string
contentType: "lectures|exercises|coursework"
```
- **Response:**
```json
{
  "success": true/false,
  "url": "string",
  "originalName": "string",
  "publicId": "string",
  "message": "string"
}
```

#### **27. POST /update-course-content**
- **Описание:** Обновяване на съдържание в курс
- **Authentication:** authenticateToken + teacher role
- **Content-Type:** multipart/form-data
- **Request Body:**
```
content: File
courseName: string
contentType: "lectures|exercises|coursework"
contentIndex: number
oldPublicId: string
```
- **Response:**
```json
{
  "success": true/false,
  "url": "string",
  "originalName": "string",
  "publicId": "string"
}
```

#### **28. POST /delete-course-content**
- **Описание:** Изтриване на съдържание от курс
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "courseName": "string",
  "contentType": "lectures|exercises|coursework",
  "contentIndex": number,
  "publicId": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

#### **29. POST /create-test**
- **Описание:** Създаване на тест
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "title": "string",
  "description": "string",
  "courseName": "string",
  "startDate": "date",
  "endDate": "date",
  "duration": number, // minutes
  "questions": [
    {
      "question": "string",
      "type": "multiple-choice|text|true-false",
      "options": ["string"],
      "correctAnswer": "string",
      "points": number
    }
  ]
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string",
  "testId": "string"
}
```

#### **30. POST /get-teacher-tests**
- **Описание:** Получаване на тестове създадени от учителя
- **Authentication:** authenticateToken + teacher role
- **Request Body:** Празно
- **Response:**
```json
{
  "success": true/false,
  "tests": [
    {
      "_id": "string",
      "title": "string",
      "courseName": "string",
      "startDate": "date",
      "endDate": "date",
      "isActive": boolean
    }
  ]
}
```

#### **31. POST /get-course-students**
- **Описание:** Получаване на студенти записани в курс
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "courseName": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "students": [
    {
      "email": "string",
      "firstName": "string",
      "lastName": "string",
      "avatarUrl": "string"
    }
  ]
}
```

#### **32. POST /get-student-submissions**
- **Описание:** Получаване на предадени работи от студент
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "studentEmail": "string",
  "courseName": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "student": {
    "firstName": "string",
    "lastName": "string",
    "email": "string"
  },
  "submissions": {
    "exercises": [
      {
        "url": "string",
        "originalName": "string",
        "grade": number
      }
    ],
    "coursework": {
      "url": "string",
      "originalName": "string",
      "grade": number
    }
  }
}
```

#### **33. POST /save-grade**
- **Описание:** Запазване на оценка
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "studentEmail": "string",
  "courseName": "string",
  "type": "exercise|coursework",
  "index": number, // за exercises
  "grade": number // 2-6 scale
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

#### **34. POST /update-grade**
- **Описание:** Обновяване на съществуваща оценка
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "studentEmail": "string",
  "courseName": "string",
  "type": "exercise|coursework",
  "index": number,
  "grade": number
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

#### **35. POST /get-student-average**
- **Описание:** Получаване на средна оценка на студент
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "studentEmail": "string",
  "courseName": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "average": number,
  "breakdown": {
    "exerciseGrades": [number],
    "courseworkGrade": number,
    "testGrades": [number]
  }
}
```

#### **36. POST /get-test-results-summary**
- **Описание:** Получаване на обобщени резултати от тест
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "testId": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "summary": {
    "testInfo": {
      "title": "string",
      "courseName": "string",
      "maxScore": number
    },
    "statistics": {
      "enrolledStudents": number,
      "totalSubmissions": number,
      "completionRate": number,
      "averageScore": number,
      "highestScore": number,
      "lowestScore": number
    }
  }
}
```

#### **37. POST /get-test-results-detailed**
- **Описание:** Получаване на детайлни резултати от тест
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "testId": "string",
  "sortBy": "submittedAt|score|timeSpent",
  "sortOrder": "asc|desc",
  "filterBy": "all|completed|incomplete"
}
```
- **Response:**
```json
{
  "success": true/false,
  "results": [
    {
      "studentInfo": {
        "firstName": "string",
        "lastName": "string",
        "email": "string"
      },
      "score": number,
      "maxScore": number,
      "timeSpent": number,
      "submittedAt": "date",
      "autoSubmitted": boolean
    }
  ]
}
```

#### **38. POST /export-test-results**
- **Описание:** Експорт на резултати от тест в CSV
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "testId": "string",
  "format": "csv",
  "includeQuestionDetails": boolean
}
```
- **Response:** CSV file download

#### **39. POST /update-test**
- **Описание:** Обновяване на тест
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "testId": "string",
  "title": "string",
  "description": "string",
  "startDate": "date",
  "endDate": "date",
  "isActive": boolean
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

#### **40. POST /delete-test**
- **Описание:** Изтриване на тест
- **Authentication:** authenticateToken + teacher role
- **Request Body:**
```json
{
  "testId": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

### **Utility Endpoints (3 броя)**

#### **41. POST /check-access**
- **Описание:** Проверка на достъп до курс
- **Authentication:** authenticateToken
- **Request Body:**
```json
{
  "courseName": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "message": "string"
}
```

#### **42. POST /get-course-by-name**
- **Описание:** Получаване на курс по име
- **Authentication:** authenticateToken
- **Request Body:**
```json
{
  "name": "string"
}
```
- **Response:**
```json
{
  "success": true/false,
  "course": {
    "_id": "string",
    "name": "string",
    "description": "string",
    "exercises": ["string"],
    "lectures": ["string"]
  }
}
```

#### **43. POST /cleanup-expired-sessions**
- **Описание:** Почистване на изтекли тест сесии
- **Authentication:** authenticateToken + teacher role
- **Request Body:** Празно
- **Response:**
```json
{
  "success": true/false,
  "expiredCount": number,
  "message": "string"
}
```

---

## 🏗️ **ТЕХНИЧЕСКА АРХИТЕКТУРА НА SERVER.JS** {#техническа-архитектура}

### **NPM Пакети и тяхната роля:**

#### **Core Framework пакети:**
```json
{
  "express": "^5.1.0",        // Web framework за Node.js
  "mongoose": "^8.14.1",      // MongoDB ODM
  "cors": "^2.8.5",           // Cross-Origin Resource Sharing
  "dotenv": "^16.5.0"         // Environment variables management
}
```

#### **Security и Authentication:**
```json
{
  "bcryptjs": "^3.0.2",       // Password hashing
  "jsonwebtoken": "^9.0.2",   // JWT token generation/verification
  "cookie-parser": "^1.4.7"   // HTTP cookie parsing
}
```

#### **File Upload и Cloud Storage:**
```json
{
  "multer": "^1.4.5-lts.2",   // Multipart/form-data handling
  "cloudinary": "^2.6.1",     // Cloud storage service
  "streamifier": "^0.1.1"     // Buffer to stream conversion
}
```

#### **Communication:**
```json
{
  "nodemailer": "^7.0.3",     // Email sending (alternative to EmailJS)
  "node-fetch": "^3.3.2"      // HTTP requests to external APIs
}
```

### **Middleware Stack организация:**

```javascript
// CORS Configuration
app.use(cors({
  origin: process.env.CLIENT_ORIGIN,
  credentials: true
}));

// Body Parsing
app.use(express.json());
app.use(cookieParser());
app.use(express.static('public'));

// File Upload Configuration
const storage = multer.memoryStorage();
const upload = multer({ storage });

// Cloudinary Configuration
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD,
  api_key: process.env.CLOUDINARY_KEY,
  api_secret: process.env.CLOUDINARY_SECRET
});
```

### **Authentication Middleware:**

```javascript
function authenticateToken(req, res, next) {
  const token = req.cookies.token;
  if (!token) {
    return res.status(401).json({
      success: false,
      message: '❌ Няма токен.'
    });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    req.user = decoded; // { email, role }
    next();
  } catch (err) {
    if (err.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: '⏳ Токенът е изтекъл.'
      });
    }
    return res.status(403).json({
      success: false,
      message: '❌ Невалиден токен.'
    });
  }
}
```

### **Database Schemas:**

#### **User Schema:**
```javascript
const userSchema = new mongoose.Schema({
  firstName: { type: String, required: true },
  lastName: { type: String, required: true },
  email: { type: String, unique: true, required: true },
  nickname: { type: String, unique: true, required: true },
  password: { type: String, required: true }, // bcrypt hashed
  role: {
    type: String,
    enum: ['student', 'teacher'],
    default: 'student'
  },
  avatarUrl: { type: String, default: '' },
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now }
});
```

#### **Course Schema:**
```javascript
const courseSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String, required: true },
  code: { type: String, unique: true, required: true },
  image: { type: String, default: '' },
  html_locked: { type: String, default: '' },
  html_unlocked: { type: String, default: '' },
  teacher: { type: String, required: true }, // teacher email
  period: { type: String, default: '' },
  category: { type: String, default: '' },
  course_work: { type: String, default: '' },
  accessors: [String], // enrolled student emails
  exercises: [String], // Cloudinary URLs
  lectures: [String],  // Cloudinary URLs
  createdAt: { type: Date, default: Date.now }
});
```

#### **Test Schema:**
```javascript
const testSchema = new mongoose.Schema({
  title: { type: String, required: true },
  description: { type: String, default: '' },
  courseName: { type: String, required: true },
  teacherEmail: { type: String, required: true },
  startDate: { type: Date, required: true },
  endDate: { type: Date, required: true },
  duration: { type: Number, required: true }, // minutes
  questions: [{
    question: { type: String, required: true },
    type: {
      type: String,
      enum: ['multiple-choice', 'text', 'true-false'],
      default: 'multiple-choice'
    },
    options: [String], // for multiple-choice
    correctAnswer: { type: String, required: true },
    points: { type: Number, default: 1 }
  }],
  isActive: { type: Boolean, default: true },
  createdAt: { type: Date, default: Date.now }
});
```

#### **Test Submission Schema:**
```javascript
const testSubmissionSchema = new mongoose.Schema({
  testId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Test',
    required: true
  },
  studentEmail: { type: String, required: true },
  courseName: { type: String, required: true },
  answers: [{
    questionIndex: { type: Number, required: true },
    answer: { type: String, required: true }
  }],
  score: { type: Number, default: 0 },
  maxScore: { type: Number, default: 0 },
  startedAt: { type: Date, required: true },
  submittedAt: { type: Date, default: Date.now },
  timeSpent: { type: Number, default: 0 }, // minutes
  isCompleted: { type: Boolean, default: false },
  autoSubmitted: { type: Boolean, default: false }
});
```

#### **Notification Schema:**
```javascript
const notificationSchema = new mongoose.Schema({
  userEmail: { type: String, required: true },
  type: {
    type: String,
    enum: ['test_available', 'test_deadline', 'exercise_submitted',
           'exercise_graded', 'coursework_graded', 'content_update'],
    required: true
  },
  title: { type: String, required: true },
  message: { type: String, required: true },
  courseName: { type: String, default: '' },
  relatedEntityId: { type: String, default: '' }, // testId, exerciseId, etc.
  isRead: { type: Boolean, default: false },
  isUrgent: { type: Boolean, default: false },
  metadata: { type: Object, default: {} }, // additional data
  expiresAt: { type: Date }, // optional expiration
  createdAt: { type: Date, default: Date.now }
});
```

### **Cloudinary интеграция и File Upload Pipeline:**

#### **Upload Configuration:**
```javascript
// Multer memory storage (no temp files)
const storage = multer.memoryStorage();
const upload = multer({
  storage,
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB limit
  fileFilter: (req, file, cb) => {
    // File type validation
    const allowedTypes = /jpeg|jpg|png|pdf|doc|docx|txt|zip|rar/;
    const extname = allowedTypes.test(
      path.extname(file.originalname).toLowerCase()
    );
    if (extname) {
      return cb(null, true);
    } else {
      cb(new Error('Invalid file type'));
    }
  }
});
```

#### **Cloudinary Upload Pipeline:**
```javascript
// Example: Exercise upload
app.post('/upload-exercise', authenticateToken,
  upload.single('exercise'), async (req, res) => {

  try {
    const user = await User.findOne({ email: req.user.email });
    const { courseName, exerciseIndex } = req.body;

    // Generate safe folder name
    const safeFolderName = courseName.replace(/[^a-zA-Z0-9]/g, '_');
    const folderName = `users/${user.nickname}/courses/${safeFolderName}/exercises`;

    // Upload to Cloudinary
    const uploadResult = await new Promise((resolve, reject) => {
      const uploadStream = cloudinary.uploader.upload_stream({
        folder: folderName,
        resource_type: 'raw', // for non-image files
        use_filename: true,
        unique_filename: true,
        display_name: req.file.originalname
      }, (error, result) => {
        if (error) reject(error);
        else resolve(result);
      });

      // Convert buffer to stream and pipe to Cloudinary
      streamifier.createReadStream(req.file.buffer).pipe(uploadStream);
    });

    // Store reference in MongoDB
    const db = mongoose.connection.db;
    await db.collection('attachments').updateOne(
      { email_user: user.email },
      {
        $set: {
          [`courses.${courseName}.exercises.${exerciseIndex}`]: {
            url: uploadResult.secure_url,
            publicId: uploadResult.public_id,
            originalName: req.file.originalname,
            uploadedAt: new Date()
          }
        }
      },
      { upsert: true }
    );

    res.json({
      success: true,
      url: uploadResult.secure_url,
      originalName: req.file.originalname,
      publicId: uploadResult.public_id
    });

  } catch (err) {
    console.error('Upload error:', err);
    res.status(500).json({
      success: false,
      message: 'File upload failed',
      error: err.message
    });
  }
});
```

### **Error Handling и Security Patterns:**

#### **Global Error Handler:**
```javascript
// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Global error handler:', err);

  if (err.name === 'ValidationError') {
    return res.status(400).json({
      success: false,
      message: 'Validation error',
      errors: Object.values(err.errors).map(e => e.message)
    });
  }

  if (err.name === 'CastError') {
    return res.status(400).json({
      success: false,
      message: 'Invalid ID format'
    });
  }

  res.status(500).json({
    success: false,
    message: 'Internal server error'
  });
});
```

#### **Role-based Access Control:**
```javascript
// Role validation helper
function requireRole(role) {
  return async (req, res, next) => {
    try {
      const user = await User.findOne({ email: req.user.email });
      if (!user || user.role !== role) {
        return res.status(403).json({
          success: false,
          message: `Access denied. ${role} role required.`
        });
      }
      next();
    } catch (err) {
      res.status(500).json({
        success: false,
        message: 'Role validation error'
      });
    }
  };
}

// Usage example
app.post('/create-test', authenticateToken, requireRole('teacher'),
  async (req, res) => {
  // Teacher-only endpoint logic
});
```

---

## 🎯 **ПРОЕКТНИ РЕШЕНИЯ И ТЕХНОЛОГИИ** {#проектни-решения}

### **Обосновка за избора на технологии:**

#### **Node.js + Express.js:**
- **JavaScript Ecosystem:** Единен език за frontend и backend
- **NPM Ecosystem:** Богата библиотека от пакети
- **Asynchronous I/O:** Отлична performance за I/O операции
- **RESTful API:** Express осигурява лесно създаване на REST endpoints
- **Middleware Pattern:** Модулна архитектура за cross-cutting concerns

#### **MongoDB Atlas:**
- **Document-based:** Естествено съответствие с JavaScript objects
- **Flexible Schema:** Лесно добавяне на нови полета без migrations
- **Cloud-native:** Automatic scaling и backup
- **Aggregation Framework:** Мощни query capabilities за analytics
- **JSON-like Documents:** Директна интеграция с JavaScript

#### **Cloudinary:**
- **CDN Delivery:** Глобално разпространение на файлове
- **Automatic Optimization:** Image compression и format conversion
- **Structured Organization:** Folder hierarchy за организация
- **Security:** Access control и malware scanning
- **API Integration:** RESTful API за programmatic access

### **JWT Authentication Implementation:**

#### **Token Generation:**
```javascript
// Login endpoint
app.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // Find user
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Невалиден имейл или парола.'
      });
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({
        success: false,
        message: 'Невалиден имейл или парола.'
      });
    }

    // Generate JWT token
    const token = jwt.sign(
      {
        email: user.email,
        role: user.role
      },
      JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Set HttpOnly cookie
    res.cookie('token', token, {
      httpOnly: true,      // Prevents XSS attacks
      sameSite: 'strict',  // CSRF protection
      secure: process.env.NODE_ENV === 'production', // HTTPS only in production
      maxAge: 24 * 60 * 60 * 1000 // 24 hours
    });

    res.json({
      success: true,
      message: 'Успешен вход.',
      user: {
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role
      }
    });

  } catch (err) {
    console.error('Login error:', err);
    res.status(500).json({
      success: false,
      message: 'Грешка при вход.'
    });
  }
});
```

### **Real-time Notification система:**

#### **Notification Creation Helper:**
```javascript
// Helper function за създаване на notifications
async function createNotification(userEmail, type, title, message, metadata = {}) {
  try {
    const notification = new Notification({
      userEmail,
      type,
      title,
      message,
      courseName: metadata.courseName || '',
      relatedEntityId: metadata.relatedEntityId || '',
      isUrgent: metadata.isUrgent || false,
      metadata: metadata,
      expiresAt: metadata.expiresAt
    });

    await notification.save();
    console.log(`✅ Notification created for ${userEmail}: ${title}`);
    return notification;
  } catch (err) {
    console.error('❌ Error creating notification:', err);
    return null;
  }
}

// Usage examples
await createNotification(
  studentEmail,
  'test_available',
  'Нов тест достъпен',
  `Тест "${testTitle}" е достъпен за курс ${courseName}`,
  {
    courseName,
    relatedEntityId: testId,
    testStartDate: test.startDate,
    testEndDate: test.endDate
  }
);

await createNotification(
  studentEmail,
  'test_deadline',
  'СПЕШНО: Тест изтича скоро!',
  `Тест "${testTitle}" изтича в рамките на 24 часа`,
  {
    courseName,
    relatedEntityId: testId,
    isUrgent: true,
    dueDate: test.endDate
  }
);
```

#### **Client-side Polling Implementation:**
```javascript
// Frontend notification polling (every 30 seconds)
export function initializeNotifications() {
  setupNotificationBell();
  loadNotifications();

  // Real-time updates
  setInterval(loadNotifications, 30000);
}

async function loadNotifications() {
  try {
    const res = await fetch('http://localhost:5000/get-notifications', {
      method: 'POST',
      credentials: 'include'
    });

    const data = await res.json();
    if (data.success) {
      notifications = data.notifications || [];
      updateNotificationDisplay();
      updateNotificationBell();
    }
  } catch (err) {
    console.error('Error loading notifications:', err);
  }
}
```

### **Test система с Time-based Constraints:**

#### **Test Access Control:**
```javascript
// Check if test is accessible
function isTestAccessible(test) {
  const now = new Date();
  return test.isActive &&
         now >= test.startDate &&
         now <= test.endDate;
}

// Start test session endpoint
app.post('/start-test-session', authenticateToken, async (req, res) => {
  try {
    const user = await User.findOne({ email: req.user.email });
    if (!user || user.role !== 'student') {
      return res.status(403).json({
        success: false,
        message: 'Access denied. Student role required.'
      });
    }

    const { testId, courseName } = req.body;
    const test = await Test.findById(testId);

    if (!test) {
      return res.status(404).json({
        success: false,
        message: 'Test not found.'
      });
    }

    // Check time-based access
    if (!isTestAccessible(test)) {
      return res.status(403).json({
        success: false,
        message: 'Test is not currently available.',
        startDate: test.startDate,
        endDate: test.endDate
      });
    }

    // Check for existing submission
    let submission = await TestSubmission.findOne({
      testId: test._id,
      studentEmail: user.email
    });

    if (submission && submission.isCompleted) {
      return res.status(400).json({
        success: false,
        message: 'Test already completed.'
      });
    }

    // Create or update submission
    if (!submission) {
      submission = new TestSubmission({
        testId: test._id,
        studentEmail: user.email,
        courseName: test.courseName,
        startedAt: new Date(),
        answers: [],
        maxScore: test.questions.reduce((sum, q) => sum + q.points, 0)
      });
      await submission.save();
    }

    // Calculate remaining time
    const sessionEndTime = new Date(
      submission.startedAt.getTime() + test.duration * 60000
    );
    const now = new Date();
    const timeRemaining = Math.max(0,
      Math.floor((sessionEndTime - now) / 1000)
    );

    // Auto-submit if time expired
    if (timeRemaining === 0) {
      submission.isCompleted = true;
      submission.autoSubmitted = true;
      submission.submittedAt = sessionEndTime;
      submission.timeSpent = test.duration;
      await submission.save();

      return res.status(400).json({
        success: false,
        message: 'Test time has expired.'
      });
    }

    // Return test without correct answers
    const testForStudent = {
      _id: test._id,
      title: test.title,
      description: test.description,
      duration: test.duration,
      questions: test.questions.map(q => ({
        question: q.question,
        type: q.type,
        options: q.options
        // correctAnswer excluded for security
      }))
    };

    res.json({
      success: true,
      test: testForStudent,
      sessionId: submission._id,
      timeRemaining
    });

  } catch (err) {
    console.error('Error starting test session:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});
```

#### **Auto-submission Logic:**
```javascript
// Submit test endpoint with auto-submission support
app.post('/submit-test', authenticateToken, async (req, res) => {
  try {
    const { sessionId, answers, isAutoSubmit = false } = req.body;

    const submission = await TestSubmission.findById(sessionId);
    if (!submission || submission.isCompleted) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or completed test session.'
      });
    }

    const test = await Test.findById(submission.testId);
    const now = new Date();
    const sessionEndTime = new Date(
      submission.startedAt.getTime() + test.duration * 60000
    );

    // Calculate time spent
    const timeSpent = Math.min(
      test.duration,
      Math.floor((now - submission.startedAt) / 60000)
    );

    // Score calculation
    let score = 0;
    answers.forEach((answer, index) => {
      const question = test.questions[index];
      if (question && answer.answer === question.correctAnswer) {
        score += question.points;
      }
    });

    // Bulgarian grading scale conversion
    const percentage = (score / submission.maxScore) * 100;
    let bulgarianGrade;
    if (percentage >= 90) bulgarianGrade = 6;      // Отличен
    else if (percentage >= 80) bulgarianGrade = 5; // Много добър
    else if (percentage >= 70) bulgarianGrade = 4; // Добър
    else if (percentage >= 60) bulgarianGrade = 3; // Задоволителен
    else bulgarianGrade = 2;                       // Слаб

    // Update submission
    submission.answers = answers;
    submission.score = score;
    submission.timeSpent = timeSpent;
    submission.isCompleted = true;
    submission.autoSubmitted = isAutoSubmit;
    submission.submittedAt = now;
    submission.bulgarianGrade = bulgarianGrade;

    await submission.save();

    // Create notification for student
    await createNotification(
      submission.studentEmail,
      'test_completed',
      'Тест завършен',
      `Вашият тест "${test.title}" е завършен с оценка ${bulgarianGrade}`,
      {
        courseName: test.courseName,
        relatedEntityId: test._id,
        score,
        maxScore: submission.maxScore,
        grade: bulgarianGrade
      }
    );

    res.json({
      success: true,
      score,
      maxScore: submission.maxScore,
      timeSpent,
      autoSubmitted: isAutoSubmit,
      bulgarianGrade
    });

  } catch (err) {
    console.error('Error submitting test:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});
```

---

## 💻 **CODE EXAMPLES И IMPLEMENTATION DETAILS** {#code-examples}

### **Database Operations Patterns:**

#### **Aggregation Pipeline за Statistics:**
```javascript
// Get course statistics
app.post('/get-course-statistics', authenticateToken, async (req, res) => {
  try {
    const { courseName } = req.body;

    // Aggregation pipeline за detailed statistics
    const stats = await TestSubmission.aggregate([
      {
        $match: {
          courseName: courseName,
          isCompleted: true
        }
      },
      {
        $group: {
          _id: '$testId',
          totalSubmissions: { $sum: 1 },
          averageScore: { $avg: '$score' },
          maxScore: { $first: '$maxScore' },
          averageTimeSpent: { $avg: '$timeSpent' },
          autoSubmittedCount: {
            $sum: { $cond: ['$autoSubmitted', 1, 0] }
          }
        }
      },
      {
        $lookup: {
          from: 'tests',
          localField: '_id',
          foreignField: '_id',
          as: 'testInfo'
        }
      },
      {
        $unwind: '$testInfo'
      },
      {
        $project: {
          testTitle: '$testInfo.title',
          totalSubmissions: 1,
          averageScore: { $round: ['$averageScore', 2] },
          maxScore: 1,
          completionRate: {
            $round: [
              { $multiply: [
                { $divide: ['$averageScore', '$maxScore'] },
                100
              ]}, 2
            ]
          },
          averageTimeSpent: { $round: ['$averageTimeSpent', 2] },
          autoSubmissionRate: {
            $round: [
              { $multiply: [
                { $divide: ['$autoSubmittedCount', '$totalSubmissions'] },
                100
              ]}, 2
            ]
          }
        }
      }
    ]);

    res.json({
      success: true,
      statistics: stats
    });

  } catch (err) {
    console.error('Error getting course statistics:', err);
    res.status(500).json({
      success: false,
      message: 'Error retrieving statistics'
    });
  }
});
```

#### **Batch Operations за Notifications:**
```javascript
// Batch create notifications for all students in course
async function notifyAllStudentsInCourse(courseName, notificationType, title, message, metadata = {}) {
  try {
    const course = await Course.findOne({ name: courseName });
    if (!course) {
      console.error('Course not found:', courseName);
      return;
    }

    const notifications = course.accessors.map(studentEmail => ({
      userEmail: studentEmail,
      type: notificationType,
      title,
      message,
      courseName,
      relatedEntityId: metadata.relatedEntityId || '',
      isUrgent: metadata.isUrgent || false,
      metadata: metadata,
      createdAt: new Date()
    }));

    // Batch insert
    await Notification.insertMany(notifications);
    console.log(`✅ Created ${notifications.length} notifications for course ${courseName}`);

  } catch (err) {
    console.error('❌ Error creating batch notifications:', err);
  }
}

// Usage example
await notifyAllStudentsInCourse(
  'JavaScript Fundamentals',
  'content_update',
  'Ново съдържание добавено',
  'Добавени са нови лекции към курса',
  {
    relatedEntityId: courseId,
    contentType: 'lectures',
    addedCount: 3
  }
);
```

### **Advanced File Management:**

#### **File Validation и Security:**
```javascript
// Advanced file validation
function validateFile(file, allowedTypes, maxSize = 10 * 1024 * 1024) {
  const errors = [];

  // Check file size
  if (file.size > maxSize) {
    errors.push(`File size exceeds ${maxSize / (1024 * 1024)}MB limit`);
  }

  // Check file type
  const fileExtension = path.extname(file.originalname).toLowerCase();
  if (!allowedTypes.includes(fileExtension)) {
    errors.push(`File type ${fileExtension} not allowed`);
  }

  // Check for potentially dangerous files
  const dangerousExtensions = ['.exe', '.bat', '.cmd', '.scr', '.pif'];
  if (dangerousExtensions.includes(fileExtension)) {
    errors.push('Potentially dangerous file type detected');
  }

  // Basic filename validation
  if (!/^[a-zA-Z0-9._-]+$/.test(file.originalname)) {
    errors.push('Invalid characters in filename');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

// Enhanced upload endpoint with validation
app.post('/upload-with-validation', authenticateToken,
  upload.single('file'), async (req, res) => {

  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file provided'
      });
    }

    // Validate file
    const allowedTypes = ['.pdf', '.doc', '.docx', '.txt', '.jpg', '.png'];
    const validation = validateFile(req.file, allowedTypes);

    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        message: 'File validation failed',
        errors: validation.errors
      });
    }

    // Proceed with upload...
    // (Cloudinary upload logic here)

  } catch (err) {
    console.error('Upload validation error:', err);
    res.status(500).json({
      success: false,
      message: 'Upload failed'
    });
  }
});
```

### **Performance Optimization Patterns:**

#### **Database Indexing:**
```javascript
// Create indexes for better query performance
async function createDatabaseIndexes() {
  try {
    // User indexes
    await User.createIndexes([
      { email: 1 },
      { nickname: 1 },
      { role: 1 }
    ]);

    // Course indexes
    await Course.createIndexes([
      { name: 1 },
      { code: 1 },
      { teacher: 1 },
      { 'accessors': 1 }
    ]);

    // Test indexes
    await Test.createIndexes([
      { courseName: 1 },
      { teacherEmail: 1 },
      { startDate: 1, endDate: 1 },
      { isActive: 1 }
    ]);

    // TestSubmission indexes
    await TestSubmission.createIndexes([
      { testId: 1, studentEmail: 1 },
      { courseName: 1 },
      { isCompleted: 1 },
      { submittedAt: 1 }
    ]);

    // Notification indexes
    await Notification.createIndexes([
      { userEmail: 1, isRead: 1 },
      { createdAt: -1 },
      { expiresAt: 1 }
    ]);

    console.log('✅ Database indexes created successfully');
  } catch (err) {
    console.error('❌ Error creating indexes:', err);
  }
}
```

#### **Caching Strategy:**
```javascript
// Simple in-memory cache for frequently accessed data
const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

function getCacheKey(prefix, ...args) {
  return `${prefix}:${args.join(':')}`;
}

function setCache(key, data) {
  cache.set(key, {
    data,
    timestamp: Date.now()
  });
}

function getCache(key) {
  const cached = cache.get(key);
  if (!cached) return null;

  if (Date.now() - cached.timestamp > CACHE_TTL) {
    cache.delete(key);
    return null;
  }

  return cached.data;
}

// Usage in endpoint
app.post('/get-course-with-cache', authenticateToken, async (req, res) => {
  try {
    const { courseName } = req.body;
    const cacheKey = getCacheKey('course', courseName);

    // Check cache first
    let course = getCache(cacheKey);

    if (!course) {
      // Fetch from database
      course = await Course.findOne({ name: courseName });
      if (course) {
        setCache(cacheKey, course);
      }
    }

    res.json({
      success: true,
      course,
      fromCache: !!getCache(cacheKey)
    });

  } catch (err) {
    console.error('Error getting course:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});
```

---

## 📋 **ЗАКЛЮЧЕНИЕ**

Тази comprehensive API документация покрива всички аспекти на студентската система за управление:

### **Ключови постижения:**
- ✅ **43 REST endpoints** с пълна документация
- ✅ **Role-based access control** с JWT authentication
- ✅ **4-tier архитектура** с cloud integration
- ✅ **Real-time notification система**
- ✅ **Advanced test система** с time constraints
- ✅ **Professional file management** с Cloudinary
- ✅ **Bulgarian academic standards** integration

### **Технически highlights:**
- **Scalable architecture** готова за production
- **Security best practices** на всички нива
- **Performance optimization** с caching и indexing
- **Comprehensive error handling**
- **Modern development patterns**

### **Готовност за разширение:**
Системата е проектирана с модулна архитектура която позволява лесно добавяне на нови функционалности, интеграция с външни системи, и scaling според нуждите на образователната институция.

---

*Документацията е създадена на български език за академични цели и може да служи като техническа референция за разработчици работещи върху системата.*
