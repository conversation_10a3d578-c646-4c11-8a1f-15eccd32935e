# Frontend HTML Елементи по Страници
## Детайлна спецификация за студентска система за управление

---

## 📋 **СЪДЪРЖАНИЕ**

1. [Общи HTML компоненти](#общи-компоненти)
2. [Студентски страници - HTML елементи](#студентски-страници)
3. [Учителски страници - HTML елементи](#учителски-страници)
4. [Споделени компоненти](#споделени-компоненти)
5. [Responsive елементи](#responsive-елементи)

---

## 🌐 **ОБЩИ HTML КОМПОНЕНТИ** {#общи-компоненти}

### **Header структура (всички страници):**
- **Logo контейнер** - изображение на университета
- **Заглавие** - "TECHNICAL COLLEGE AT TECHNICAL UNIVERSITY - SOFIA"
- **Потребителска информация** - аватар, име, dropdown меню
- **Notification bell** - иконка с червена точка за непрочетени известия
- **Logout бутон** - бутон за изход от системата

### **Aside меню структура (всички страници):**
- **Logo секция** - лого на студентската система
- **Основна навигация** - списък с връзки (различни за всяка роля)
- **Разделител** - хоризонтална линия
- **"Моите Курсове" секция** - заглавие и динамичен списък

### **Main content wrapper:**
- **Основен контейнер** - централна област за съдържание
- **Card компоненти** - контейнери за различни секции
- **Button елементи** - различни типове бутони според функцията

---

## 👨‍🎓 **СТУДЕНТСКИ СТРАНИЦИ - HTML ЕЛЕМЕНТИ** {#студентски-страници}

### **1. Dashboard (dashboard.html)**

#### **Основни елементи:**
- **Приветствено съобщение** - h2 заглавие с име на студента
- **Подзаглавие** - мотивиращ текст за започване на обучението
- **Feature cards контейнер** - grid layout с 3 карти

#### **Feature cards (3 броя):**
1. **Моите Курсове карта:**
   - Иконка (fas fa-book-open)
   - Заглавие "Моите Курсове"
   - Описание "Преглед на всички ваши курсове"

2. **Задачи карта:**
   - Иконка (fas fa-tasks)
   - Заглавие "Задачи"
   - Описание "Следете текущите си задачи"

3. **Прогрес карта:**
   - Иконка (fas fa-chart-bar)
   - Заглавие "Прогрес"
   - Описание "Анализ на вашите постижения"

#### **Aside меню навигация:**
- Начало (активна)
- Моите Курсове
- Задачи
- Прогрес
- Всички курсове (с hover panel)

### **2. Моите Курсове (my-courses.html)**

#### **Header секция:**
- **Заглавие страница** - "Моите Курсове"
- **View toggle бутони** - Grid view / List view
- **Филтър опции** - dropdown за сортиране

#### **Статистики панел:**
- **Общо курсове** - число и иконка
- **Активни тестове** - число и иконка
- **Средна оценка** - число и иконка
- **Завършени задачи** - число и иконка

#### **Курсове контейнер:**
- **Grid/List layout** - адаптивна мрежа или списък
- **Course cards** - за всеки курс:
  - Изображение на курса
  - Заглавие на курса
  - Описание
  - Име на преподавателя
  - Прогрес бар
  - "Влез в курса" бутон

#### **Test notifications секция:**
- **Заглавие** - "Активни тестове"
- **Test cards** - за всеки тест:
  - Име на теста
  - Курс
  - Начална/крайна дата
  - Статус (активен/предстоящ)
  - "Започни тест" бутон

### **3. Задачи (my-tasks.html)**

#### **Филтър секция:**
- **Search bar** - поле за търсене
- **Category filters** - бутони за филтриране по тип
- **Date range picker** - избор на период

#### **Task summary cards (3 секции):**

1. **Спешни задачи (червен цвят):**
   - Заглавие "Спешни задачи"
   - Брой задачи
   - Списък с активни тестове

2. **Завършени задачи (зелен цвят):**
   - Заглавие "Завършени"
   - Брой завършени
   - Списък с оценени работи

3. **Предстоящи задачи (син цвят):**
   - Заглавие "Предстоящи"
   - Брой предстоящи
   - Списък с бъдещи задачи

#### **Детайлен списък:**
- **Task items** - за всяка задача:
  - Тип иконка
  - Заглавие
  - Курс
  - Краен срок
  - Статус badge
  - Action бутон

### **4. Прогрес (my-progress.html)**

#### **Progress header:**
- **Заглавие** - "Моят прогрес"
- **Общ GPA** - голямо число с описание
- **Период selector** - dropdown за избор на период

#### **Charts секция:**
- **Progress chart контейнер** - canvas елемент за графика
- **Legend** - легенда за графиката
- **Chart controls** - бутони за различни изгледи

#### **Course progress таблица:**
- **Table header** - колони за курс, оценки, прогрес
- **Table rows** - за всеки курс:
  - Име на курса
  - Упражнения оценка
  - Курсова работа оценка
  - Тестове оценка
  - Финална оценка
  - Progress bar

#### **Achievements секция:**
- **Заглавие** - "Постижения"
- **Achievement badges** - иконки с описания
- **Statistics cards** - допълнителни статистики

### **5. Всички Курсове (courses.html)**

#### **Search и filter секция:**
- **Search input** - поле за търсене по име
- **Category dropdown** - филтър по категория
- **Teacher dropdown** - филтър по преподавател
- **Clear filters бутон** - изчистване на филтрите

#### **Enrollment panel:**
- **Заглавие** - "Записване в курс"
- **Code input** - поле за въвеждане на код
- **Submit бутон** - "Запиши се"
- **Help text** - инструкции за кода

#### **Courses grid:**
- **Course cards** - за всеки курс:
  - Изображение
  - Заглавие
  - Кратко описание
  - Преподавател
  - Категория badge
  - Enrollment статус
  - Action бутон (Запиши се/Вече записан)

### **6. Профил (profile.html)**

#### **Profile header:**
- **Avatar секция** - голяма профилна снимка
- **Upload бутон** - за смяна на аватара
- **Име и email** - основна информация

#### **Profile information:**
- **Personal info card:**
  - Име поле (readonly)
  - Фамилия поле (readonly)
  - Email поле (readonly)
  - Потребителско име поле (readonly)
  - Роля badge

#### **Settings секция:**
- **Password change card:**
  - Текуща парола поле
  - Нова парола поле
  - Потвърди парола поле
  - "Смени парола" бутон

- **Notification settings:**
  - Email notifications checkbox
  - Push notifications checkbox
  - Test reminders checkbox

### **7. Тест (test.html)**

#### **Test header:**
- **Test title** - заглавие на теста
- **Timer display** - оставащо време (MM:SS)
- **Progress indicator** - въпрос X от Y
- **Auto-submit warning** - предупреждение за автоматично подаване

#### **Question container:**
- **Question text** - текст на въпроса
- **Question type indicator** - тип въпрос
- **Answer options** - според типа:
  - Radio buttons (multiple choice)
  - Text area (text questions)
  - Checkboxes (true/false)

#### **Navigation controls:**
- **Previous бутон** - предишен въпрос
- **Next бутон** - следващ въпрос
- **Question navigator** - номерирани бутони за всеки въпрос
- **Submit test бутон** - подаване на теста

#### **Progress sidebar:**
- **Answered questions** - списък с отговорени
- **Unanswered questions** - списък с неотговорени
- **Flagged questions** - маркирани за преглед

### **8. Заключен Курс (courses_locked/)**

#### **Course info display:**
- **Course banner** - изображение на курса
- **Course title** - заглавие
- **Course description** - описание
- **Teacher info** - име на преподавателя
- **Lock message** - съобщение за необходимост от код

#### **Unlock form:**
- **Code input field** - поле за въвеждане на код
- **Unlock бутон** - "Отключи курса"
- **Help text** - инструкции за получаване на код

### **9. Отключен Курс (courses_unlocked/)**

#### **Course header:**
- **Course banner** - изображение
- **Course info panel:**
  - Заглавие на курса
  - Описание
  - Преподавател
  - Период
  - Прогрес бар

#### **Content sections (3 табове):**

1. **Лекции таб:**
   - Списък с лекции
   - Download links
   - View online бутони

2. **Упражнения таб:**
   - Списък с упражнения
   - Upload form за решения
   - Submitted files списък
   - Оценки display

3. **Курсови работи таб:**
   - Описание на курсовата работа
   - Upload form
   - Submission status
   - Оценка и feedback

#### **Tests секция:**
- **Available tests** - списък с тестове
- **Test cards** - за всеки тест:
  - Заглавие
  - Описание
  - Времеви рамки
  - Статус (активен/завършен/предстоящ)
  - Action бутон

#### **Progress tracking:**
- **Completion percentage** - общ прогрес
- **Individual progress bars** - за всяка секция
- **Grade summary** - обобщение на оценките

---

## 👨‍🏫 **УЧИТЕЛСКИ СТРАНИЦИ - HTML ЕЛЕМЕНТИ** {#учителски-страници}

### **1. Teacher Dashboard (dashboard.html)**

#### **Основни елементи:**
- **Приветствено съобщение** - h2 заглавие с име на учителя
- **Подзаглавие** - "Управлявайте курсовете и следете прогреса на студентите!"
- **Teacher feature cards** - grid layout с 3 карти

#### **Teacher feature cards:**
1. **Учителско табло карта:**
   - Иконка (fas fa-chalkboard-teacher)
   - Заглавие "Учителско табло"
   - Описание "Управление на студенти и оценки"

2. **Създай тест карта:**
   - Иконка (fas fa-plus-circle)
   - Заглавие "Създай тест"
   - Описание "Създавайте тестове за студентите"

3. **Планирани тестове карта:**
   - Иконка (fas fa-calendar-alt)
   - Заглавие "Планирани тестове"
   - Описание "Преглед на всички ваши тестове"

#### **Aside меню навигация:**
- Начало (активна)
- Всички курсове
- Учителско табло
- Създай тест
- Моите планирани тестове

### **2. Всички Курсове (courses.html)**

#### **Page header:**
- **Заглавие** - "Управление на курсове"
- **Create course бутон** - "Създай нов курс"
- **View options** - Grid/List toggle

#### **Course management tools:**
- **Search bar** - търсене по име на курс
- **Filter dropdown** - филтър по категория/преподавател
- **Sort options** - сортиране по дата/име/студенти

#### **Courses grid:**
- **Course management cards** - за всеки курс:
  - Изображение на курса
  - Заглавие и описание
  - Преподавател
  - Брой записани студенти
  - Статус на курса
  - Management бутони:
    - "Редактирай"
    - "Управлявай студенти"
    - "Виж статистики"
    - "Архивирай"

#### **Create course modal:**
- **Course info form:**
  - Име на курса поле
  - Описание textarea
  - Категория dropdown
  - Период поле
  - Image upload
- **Submit бутон** - "Създай курса"
- **Cancel бутон** - "Отказ"

### **3. Учителско табло (teacher-dashboard.html)**

#### **Course selection:**
- **Заглавие** - "Изберете курс за управление"
- **Course dropdown** - списък с курсовете на учителя
- **Load course бутон** - "Зареди курса"

#### **Students overview (след избор на курс):**
- **Course info panel:**
  - Име на курса
  - Брой студенти
  - Активни тестове
  - Средна оценка на класа

#### **Students grid:**
- **Student cards** - за всеки студент:
  - Аватар на студента
  - Име и email
  - Общ прогрес бар
  - Средна оценка
  - "Виж детайли" бутон

#### **Student details modal:**
- **Student info:**
  - Лична информация
  - Enrollment дата
  - Общ прогрес

- **Submissions sections:**
  1. **Упражнения секция:**
     - Списък с предадени упражнения
     - Grade input полета
     - "Запази оценка" бутони

  2. **Курсови работи секция:**
     - Предадени курсови работи
     - Download links
     - Grade и feedback форми

  3. **Тестове секция:**
     - Взети тестове
     - Резултати и оценки
     - Детайлен преглед на отговорите

### **4. Създаване на Тест (create-test.html)**

#### **Test basic info:**
- **Test info form:**
  - Заглавие на теста поле
  - Описание textarea
  - Курс dropdown
  - Продължителност (минути) поле

#### **Schedule settings:**
- **Start date/time picker** - начална дата и час
- **End date/time picker** - крайна дата и час
- **Timezone display** - показване на часовата зона

#### **Questions builder:**
- **Add question бутон** - "Добави въпрос"
- **Questions list** - списък с добавени въпроси

#### **Question form (за всеки въпрос):**
- **Question type dropdown:**
  - Multiple choice
  - Text answer
  - True/False

- **Question text** - textarea за въпроса
- **Points input** - точки за въпроса

- **Answer options (за multiple choice):**
  - Option 1 поле
  - Option 2 поле
  - Option 3 поле
  - Option 4 поле
  - Correct answer radio buttons

- **Question controls:**
  - "Запази въпроса" бутон
  - "Изтрий въпроса" бутон
  - "Премести нагоре/надолу" бутони

#### **Test preview:**
- **Preview бутон** - "Прегледай теста"
- **Publish бутон** - "Публикувай теста"
- **Save draft бутон** - "Запази като чернова"

### **5. Планирани Тестове (my-scheduled-tests.html)**

#### **Tests overview:**
- **Filter tabs:**
  - Всички тестове
  - Активни
  - Предстоящи
  - Завършени

#### **Tests grid:**
- **Test management cards** - за всеки тест:
  - Заглавие на теста
  - Курс
  - Период на активност
  - Брой участници
  - Статус badge
  - Management бутони:
    - "Редактирай"
    - "Виж резултати"
    - "Експортирай"
    - "Изтрий"

#### **Test statistics panel:**
- **Quick stats:**
  - Общо тестове
  - Активни тестове
  - Общо участници
  - Средна успеваемост

### **6. Резултати от Тест (test-results.html)**

#### **Test info header:**
- **Test details:**
  - Заглавие на теста
  - Курс
  - Период на провеждане
  - Общо участници

#### **Summary statistics:**
- **Stats cards:**
  - Средна оценка
  - Процент успеваемост
  - Най-висока оценка
  - Най-ниска оценка

#### **Results visualization:**
- **Charts container:**
  - Grade distribution chart
  - Question difficulty chart
  - Time spent chart

#### **Detailed results table:**
- **Table header:**
  - Студент
  - Email
  - Оценка
  - Процент
  - Време
  - Статус

- **Table rows** - за всеки студент:
  - Име на студента
  - Email адрес
  - Получена оценка
  - Процент правилни отговори
  - Време за решаване
  - Статус (завършен/автоматично подаден)
  - "Виж детайли" бутон

#### **Export options:**
- **Export бутони:**
  - "Експортирай в CSV"
  - "Експортирай в PDF"
  - "Изпрати по email"

### **7. Управление на Курс (courses_unlocked/teacher_template)**

#### **Course management header:**
- **Course info display:**
  - Заглавие на курса
  - Описание
  - Брой студенти
  - Последна активност

#### **Content management tabs:**

1. **Лекции управление:**
   - **Upload form:**
     - File input за лекции
     - Заглавие поле
     - Описание textarea
     - "Качи лекция" бутон

   - **Lectures list:**
     - Списък с качени лекции
     - Edit/Delete бутони за всяка

2. **Упражнения управление:**
   - **Upload form за упражнения**
   - **Exercises list с management опции**
   - **Student submissions преглед**

3. **Курсови работи управление:**
   - **Assignment creation form**
   - **Submissions от студенти**
   - **Grading interface**

#### **Student management panel:**
- **Enrolled students list:**
  - Списък с записани студенти
  - Remove student опция
  - Send notification бутон

- **Enrollment management:**
  - Course code display
  - Generate new code бутон
  - Enrollment settings

#### **Course tests section:**
- **Tests for this course:**
  - Списък с тестове за курса
  - Quick access към test creation
  - Test results links

### **8. Профил (profile.html)**

#### **Teacher profile header:**
- **Avatar секция** - профилна снимка
- **Upload бутон** - смяна на аватара
- **Teacher info** - име, email, роля

#### **Professional information:**
- **Teaching info card:**
  - Име и фамилия (readonly)
  - Email (readonly)
  - Потребителско име (readonly)
  - Роля badge "Teacher"
  - Департамент поле (optional)

#### **Teacher settings:**
- **Password change** - същото като при студенти
- **Notification preferences:**
  - Student submission notifications
  - Test completion alerts
  - Course enrollment notifications
  - Grade deadline reminders

- **Teaching preferences:**
  - Default test duration
  - Auto-grading settings
  - Email signature template

---

## 🌐 **СПОДЕЛЕНИ КОМПОНЕНТИ** {#споделени-компоненти}

### **1. Начална страница (index.html)**

#### **Header секция:**
- **University logo** - изображение на ТУ София
- **Main title** - заглавие на системата
- **Navigation buttons:**
  - "Влез" бутон
  - "Регистрация" бутон

#### **Hero section:**
- **Welcome message** - приветствено съобщение
- **System description** - описание на възможностите
- **Call-to-action бутони**

#### **Features showcase:**
- **Feature cards (3 броя):**
  1. Онлайн курсове
  2. Тестове и оценяване
  3. Прогрес проследяване

### **2. Вход (login.html)**

#### **Login form:**
- **Email input** - поле за email
- **Password input** - поле за парола
- **Remember me checkbox** - запомни ме
- **Login бутон** - "Влез в системата"
- **Forgot password link** - връзка за забравена парола
- **Register link** - връзка към регистрация

#### **Form validation:**
- **Error messages container** - за показване на грешки
- **Success messages container** - за потвърждения

### **3. Регистрация (registration.html)**

#### **Registration form:**
- **Personal info section:**
  - Име поле
  - Фамилия поле
  - Email поле
  - Потребителско име поле

- **Account settings:**
  - Парола поле
  - Потвърди парола поле
  - Роля dropdown (Student/Teacher)

- **Form controls:**
  - "Регистрирай се" бутон
  - "Имам профил" връзка към login

#### **Terms and conditions:**
- **Checkbox** - съгласие с условията
- **Terms link** - връзка към пълните условия

---

## 📱 **RESPONSIVE ЕЛЕМЕНТИ** {#responsive-елементи}

### **Mobile Navigation:**

#### **Mobile header:**
- **Hamburger menu бутон** - за отваряне на менюто
- **Mobile logo** - по-малко лого
- **Mobile user menu** - компактен потребителски панел

#### **Mobile aside menu:**
- **Collapsible navigation** - свиваемо меню
- **Touch-friendly buttons** - по-големи бутони за докосване
- **Swipe gestures** - поддръжка за swipe навигация

### **Tablet Adaptations:**

#### **Grid adjustments:**
- **2-column layouts** - вместо 3-4 колони
- **Larger touch targets** - по-големи области за докосване
- **Optimized spacing** - подходящи разстояния

### **Desktop Enhancements:**

#### **Advanced features:**
- **Hover effects** - интерактивни ефекти при hover
- **Keyboard shortcuts** - клавиатурни комбинации
- **Multi-column layouts** - максимално използване на пространството

---

## 🎨 **СПЕЦИАЛНИ UI КОМПОНЕНТИ**

### **Notification System:**

#### **Notification bell:**
- **Bell icon** - иконка за известия
- **Red dot indicator** - червена точка за непрочетени
- **Dropdown panel** - падащ панел с известия

#### **Notification items:**
- **Notification icon** - тип на известието
- **Title и message** - заглавие и съобщение
- **Timestamp** - време на получаване
- **Mark as read бутон** - маркиране като прочетено

### **Progress Components:**

#### **Progress bars:**
- **Percentage display** - показване на процент
- **Color coding** - различни цветове според прогреса
- **Animated transitions** - плавни анимации

#### **Charts и graphs:**
- **Canvas elements** - за рисуване на графики
- **Legend components** - легенди за графиките
- **Interactive tooltips** - информативни подсказки

### **Form Components:**

#### **File upload:**
- **Drag & drop area** - зона за влачене на файлове
- **Progress indicator** - индикатор за прогрес на качването
- **File preview** - преглед на избрания файл
- **Upload status** - статус на качването

#### **Date/time pickers:**
- **Calendar popup** - календар за избор на дата
- **Time selector** - избор на час
- **Timezone display** - показване на часова зона

### **Modal Components:**

#### **Modal structure:**
- **Overlay background** - затъмнен фон
- **Modal container** - основен контейнер
- **Header с close бутон** - заглавие и затваряне
- **Body content** - основно съдържание
- **Footer с action бутони** - бутони за действия

### **Table Components:**

#### **Data tables:**
- **Sortable headers** - сортируеми колони
- **Filter inputs** - полета за филтриране
- **Pagination controls** - контроли за страници
- **Row selection** - избор на редове
- **Export buttons** - бутони за експорт

### **Card Components:**

#### **Content cards:**
- **Card header** - заглавна част
- **Card body** - основно съдържание
- **Card footer** - долна част с действия
- **Card actions** - бутони за действия

---

## 📋 **ЗАКЛЮЧЕНИЕ**

### **Общо HTML елементи по страници:**

#### **Студентски страници:**
- **9 основни страници** с общо над 150 уникални HTML компонента
- **Специализирани форми** за upload, тестове, и профил управление
- **Интерактивни елементи** за навигация и взаимодействие

#### **Учителски страници:**
- **8 основни страници** с над 200 управленски HTML компонента
- **Сложни форми** за създаване на курсове и тестове
- **Advanced таблици** за управление на данни и статистики

#### **Споделени компоненти:**
- **3 общи страници** с основни authentication елементи
- **Responsive адаптации** за всички размери екрани
- **Reusable компоненти** за консистентност

### **Техническа спецификация:**
Тази документация служи като **comprehensive reference** за всички HTML елементи необходими за имплементация на пълнофункционална студентска система за управление с role-based интерфейси.

---

*Документацията покрива всички необходими HTML елементи за създаване на професионална образователна платформа с модерен и интуитивен потребителски интерфейс.*
