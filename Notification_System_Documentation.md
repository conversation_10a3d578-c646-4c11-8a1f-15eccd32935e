# Notification System Documentation
## Comprehensive анализ на системата за известия в студентската система за управление

---

## 📋 **СЪДЪРЖАНИЕ**

1. [Архитектура на notification системата](#архитектура)
2. [API endpoints за notifications](#api-endpoints)
3. [Student notification функционалности](#student-notifications)
4. [Teacher notification функционалности](#teacher-notifications)
5. [Technical implementation details](#technical-implementation)
6. [Workflow диаграми](#workflow-диаграми)

---

## 🏗️ **АРХИТЕКТУРА НА NOTIFICATION СИСТЕМАТА** {#архитектура}

### **Real-time Polling Механизъм:**

#### **30-Second Interval System:**
Notification системата използва client-side polling механизъм който проверява за нови известия на всеки 30 секунди. Този подход осигурява баланс между real-time актуализации и server performance.

**Polling Process Flow:**
1. **Initialization** - При зареждане на страница се стартира notification система
2. **Initial Load** - Веднага се зареждат съществуващите notifications
3. **Interval Setup** - Стартира се setInterval за проверки на всеки 30 секунди
4. **Background Checks** - Автоматични API заявки без прекъсване на user experience
5. **UI Updates** - Динамично обновяване на notification bell и dropdown

#### **Performance Considerations:**
- **Lightweight Requests** - Минимални API заявки за оптимизация
- **Caching Strategy** - Локално съхранение за намаляване на server load
- **Error Handling** - Graceful degradation при network issues
- **Battery Optimization** - Intelligent polling frequency adjustment

### **Database Schema за Notifications:**

#### **Notification Model Structure:**
```javascript
const notificationSchema = new mongoose.Schema({
  userEmail: { 
    type: String, 
    required: true,
    index: true // За бърз достъп по потребител
  },
  type: { 
    type: String, 
    enum: [
      'test_available',      // Нов тест достъпен
      'test_deadline',       // Приближаващ краен срок
      'exercise_submitted',  // Предадено упражнение (за учители)
      'exercise_graded',     // Оценено упражнение (за студенти)
      'coursework_graded',   // Оценена курсова работа
      'content_update',      // Ново съдържание в курс
      'test_completed',      // Завършен тест (за учители)
      'course_enrollment'    // Нов студент в курс
    ],
    required: true 
  },
  title: { 
    type: String, 
    required: true,
    maxlength: 100
  },
  message: { 
    type: String, 
    required: true,
    maxlength: 500
  },
  courseName: { 
    type: String, 
    default: '',
    index: true // За филтриране по курс
  },
  relatedEntityId: { 
    type: String, 
    default: '' // testId, exerciseId, etc.
  },
  isRead: { 
    type: Boolean, 
    default: false,
    index: true // За бърз достъп до непрочетени
  },
  isUrgent: { 
    type: Boolean, 
    default: false
  },
  metadata: { 
    type: Object, 
    default: {} // Допълнителни данни
  },
  expiresAt: { 
    type: Date // Автоматично изтриване
  },
  createdAt: { 
    type: Date, 
    default: Date.now,
    index: true // За сортиране по дата
  }
});
```

#### **Database Indexes:**
```javascript
// Composite indexes за оптимизация
notificationSchema.index({ userEmail: 1, isRead: 1 });
notificationSchema.index({ userEmail: 1, createdAt: -1 });
notificationSchema.index({ expiresAt: 1 }, { expireAfterSeconds: 0 });
```

### **Типове Notifications според Роля:**

#### **Student Notifications:**
- **test_available** - Нов тест е достъпен за взимане
- **test_deadline** - Тест изтича в рамките на 24 часа (urgent)
- **exercise_graded** - Получена оценка за упражнение
- **coursework_graded** - Получена оценка за курсова работа
- **content_update** - Добавени нови материали в курс
- **course_enrollment** - Успешно записване в курс

#### **Teacher Notifications:**
- **exercise_submitted** - Студент е предал упражнение
- **test_completed** - Студент е завършил тест
- **course_enrollment** - Нов студент се е записал в курс
- **content_update** - Потвърждение за качено съдържание
- **test_deadline** - Напомняне за приближаващ тест

### **Frontend-Backend Integration Patterns:**

#### **Notification Flow Architecture:**
```
Event Trigger → Server Logic → Database Insert → Client Polling → UI Update
     ↓              ↓              ↓              ↓              ↓
User Action → Notification → MongoDB Store → API Response → DOM Manipulation
```

#### **Integration Components:**
1. **Server-side Triggers** - Автоматично създаване при events
2. **Database Layer** - Persistent storage с indexing
3. **API Layer** - RESTful endpoints за CRUD operations
4. **Client Polling** - JavaScript interval-based requests
5. **UI Components** - Dynamic notification display

---

## 🔗 **API ENDPOINTS ЗА NOTIFICATIONS** {#api-endpoints}

### **1. Get Notifications - `/get-notifications`**

#### **Method:** POST
#### **Authentication:** Required (JWT Cookie)
#### **Role Access:** Both Student & Teacher

#### **Request Format:**
```javascript
// Headers
{
  "Content-Type": "application/json",
  "Cookie": "token=jwt_token_here"
}

// Body (optional filters)
{
  "limit": 20,           // Максимален брой notifications
  "offset": 0,           // За pagination
  "unreadOnly": false,   // Само непрочетени
  "courseName": ""       // Филтър по курс
}
```

#### **Response Format:**
```javascript
// Success Response
{
  "success": true,
  "notifications": [
    {
      "_id": "notification_id",
      "type": "test_available",
      "title": "Нов тест достъпен",
      "message": "Тест 'JavaScript Fundamentals' е достъпен за курс 'Web Development'",
      "courseName": "Web Development",
      "relatedEntityId": "test_id_123",
      "isRead": false,
      "isUrgent": false,
      "metadata": {
        "testStartDate": "2024-01-15T10:00:00Z",
        "testEndDate": "2024-01-15T12:00:00Z"
      },
      "createdAt": "2024-01-14T15:30:00Z"
    }
  ],
  "unreadCount": 5,
  "totalCount": 25
}

// Error Response
{
  "success": false,
  "message": "Unauthorized access"
}
```

### **2. Mark Notification as Read - `/mark-notification-read`**

#### **Method:** POST
#### **Authentication:** Required
#### **Role Access:** Both Student & Teacher

#### **Request Format:**
```javascript
{
  "notificationId": "notification_id_here"
}
```

#### **Response Format:**
```javascript
// Success
{
  "success": true,
  "message": "Известието е маркирано като прочетено"
}

// Error
{
  "success": false,
  "message": "Известието не е намерено"
}
```

### **3. Mark All Notifications as Read - `/mark-all-notifications-read`**

#### **Method:** POST
#### **Authentication:** Required
#### **Role Access:** Both Student & Teacher

#### **Request Format:**
```javascript
{} // Empty body
```

#### **Response Format:**
```javascript
{
  "success": true,
  "message": "Всички известия са маркирани като прочетени",
  "updatedCount": 8
}
```

### **4. Get Student Notifications (Specialized) - `/get-student-notifications`**

#### **Method:** POST
#### **Authentication:** Required
#### **Role Access:** Student Only

#### **Request Format:**
```javascript
{
  "testNotificationsOnly": true // Само test-related notifications
}
```

#### **Response Format:**
```javascript
{
  "success": true,
  "notifications": [
    {
      "type": "test_deadline",
      "title": "СПЕШНО: Тест изтича скоро!",
      "message": "Тест 'Database Design' изтича в рамките на 2 часа",
      "courseName": "Database Systems",
      "isUrgent": true,
      "metadata": {
        "timeRemaining": "02:15:30",
        "testId": "test_456"
      }
    }
  ]
}
```

### **5. Create Notification (Internal) - `/create-notification`**

#### **Method:** POST
#### **Authentication:** Required
#### **Role Access:** Teacher Only (for manual notifications)

#### **Request Format:**
```javascript
{
  "recipientEmails": ["<EMAIL>", "<EMAIL>"],
  "type": "content_update",
  "title": "Нови материали добавени",
  "message": "Добавени са нови лекции към курса",
  "courseName": "Web Development",
  "isUrgent": false,
  "metadata": {
    "contentType": "lectures",
    "addedCount": 3
  }
}
```

#### **Response Format:**
```javascript
{
  "success": true,
  "message": "Създадени са 2 известия",
  "createdCount": 2
}
```

---

## 👨‍🎓 **STUDENT NOTIFICATION ФУНКЦИОНАЛНОСТИ** {#student-notifications}

### **Типове Student Notifications:**

#### **1. Test Available Notifications:**
**Trigger Event:** Учител активира нов тест
**Creation Logic:**
```javascript
// Автоматично създаване при test activation
async function createTestAvailableNotifications(test) {
  const course = await Course.findOne({ name: test.courseName });
  const studentEmails = course.accessors; // Всички записани студенти

  const notifications = studentEmails.map(email => ({
    userEmail: email,
    type: 'test_available',
    title: 'Нов тест достъпен',
    message: `Тест "${test.title}" е достъпен за курс ${test.courseName}`,
    courseName: test.courseName,
    relatedEntityId: test._id,
    metadata: {
      testStartDate: test.startDate,
      testEndDate: test.endDate,
      duration: test.duration
    }
  }));

  await Notification.insertMany(notifications);
}
```

#### **2. Test Deadline Notifications (Urgent):**
**Trigger Event:** 24 часа преди изтичане на тест
**Creation Logic:**
```javascript
// Scheduled job за deadline warnings
async function createTestDeadlineWarnings() {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);

  const expiringTests = await Test.find({
    endDate: { $lte: tomorrow, $gte: new Date() },
    isActive: true
  });

  for (const test of expiringTests) {
    const course = await Course.findOne({ name: test.courseName });

    // Създаване на urgent notifications
    const notifications = course.accessors.map(email => ({
      userEmail: email,
      type: 'test_deadline',
      title: 'СПЕШНО: Тест изтича скоро!',
      message: `Тест "${test.title}" изтича в рамките на 24 часа`,
      courseName: test.courseName,
      relatedEntityId: test._id,
      isUrgent: true,
      metadata: {
        dueDate: test.endDate,
        timeRemaining: calculateTimeRemaining(test.endDate)
      }
    }));

    await Notification.insertMany(notifications);
  }
}
```

#### **3. Exercise Graded Notifications:**
**Trigger Event:** Учител поставя оценка на упражнение
**Creation Logic:**
```javascript
// При запазване на оценка
async function createExerciseGradedNotification(gradeData) {
  const notification = new Notification({
    userEmail: gradeData.studentEmail,
    type: 'exercise_graded',
    title: 'Получихте оценка за упражнение',
    message: `Вашето упражнение ${gradeData.exerciseIndex} за курс ${gradeData.courseName} е оценено с ${gradeData.grade}`,
    courseName: gradeData.courseName,
    relatedEntityId: gradeData.exerciseId,
    metadata: {
      grade: gradeData.grade,
      exerciseIndex: gradeData.exerciseIndex,
      gradedAt: new Date()
    }
  });

  await notification.save();
}
```

### **Frontend Implementation за Student Notifications:**

#### **Notification Bell Component:**
```javascript
// notifications.js - Student notification system
let notifications = [];
let unreadCount = 0;

export function initializeNotifications() {
  setupNotificationBell();
  loadNotifications();

  // Real-time updates every 30 seconds
  setInterval(loadNotifications, 30000);
}

function setupNotificationBell() {
  const notificationBell = document.getElementById('notificationBell');
  const notificationDropdown = document.getElementById('notificationDropdown');

  if (!notificationBell) return;

  // Toggle dropdown visibility
  notificationBell.addEventListener('click', (e) => {
    e.stopPropagation();
    notificationDropdown.classList.toggle('show');

    // Mark notifications as seen when opened
    if (notificationDropdown.classList.contains('show')) {
      markVisibleNotificationsAsSeen();
    }
  });

  // Close dropdown when clicking outside
  document.addEventListener('click', (e) => {
    if (!notificationBell.contains(e.target)) {
      notificationDropdown.classList.remove('show');
    }
  });
}

async function loadNotifications() {
  try {
    const res = await fetch('http://localhost:5000/get-notifications', {
      method: 'POST',
      credentials: 'include'
    });

    const data = await res.json();
    if (data.success) {
      notifications = data.notifications || [];
      updateNotificationDisplay();
      updateNotificationBell();
    }
  } catch (err) {
    console.error('Error loading notifications:', err);
  }
}
```

#### **Notification Display Logic:**
```javascript
function updateNotificationDisplay() {
  const notificationList = document.getElementById('notificationList');
  const notificationDot = document.getElementById('notificationDot');

  // Update unread count
  unreadCount = notifications.filter(n => !n.isRead).length;

  // Show/hide red dot indicator
  if (notificationDot) {
    notificationDot.style.display = unreadCount > 0 ? 'block' : 'none';
    notificationDot.textContent = unreadCount > 9 ? '9+' : unreadCount;
  }

  // Populate notification list
  if (notifications.length === 0) {
    notificationList.innerHTML = `
      <div class="no-notifications">
        <i class="fas fa-bell-slash"></i>
        <p>Няма нови известия</p>
      </div>
    `;
  } else {
    notificationList.innerHTML = '';

    // Sort by urgency and date
    const sortedNotifications = notifications.sort((a, b) => {
      if (a.isUrgent !== b.isUrgent) {
        return b.isUrgent - a.isUrgent; // Urgent first
      }
      return new Date(b.createdAt) - new Date(a.createdAt); // Newest first
    });

    sortedNotifications.forEach(notification => {
      const notificationElement = createNotificationElement(notification);
      notificationList.appendChild(notificationElement);
    });
  }
}

function createNotificationElement(notification) {
  const element = document.createElement('div');
  element.className = `notification-item ${!notification.isRead ? 'unread' : ''} ${notification.isUrgent ? 'urgent' : ''}`;
  element.dataset.notificationId = notification._id;

  const timeAgo = getTimeAgo(new Date(notification.createdAt));
  const urgencyBadge = notification.isUrgent ? '<span class="urgent-badge">СПЕШНО</span>' : '';

  element.innerHTML = `
    <div class="notification-icon">
      <i class="${getNotificationIcon(notification.type)}"></i>
    </div>
    <div class="notification-content">
      <div class="notification-header">
        <h4>${notification.title}</h4>
        ${urgencyBadge}
        <span class="notification-time">${timeAgo}</span>
      </div>
      <p class="notification-message">${notification.message}</p>
      ${notification.courseName ? `<span class="course-badge">${notification.courseName}</span>` : ''}
    </div>
    <div class="notification-actions">
      ${!notification.isRead ? '<button class="mark-read-btn" onclick="markAsRead(\'' + notification._id + '\')">Маркирай като прочетено</button>' : ''}
    </div>
  `;

  // Add click handler for navigation
  if (notification.relatedEntityId) {
    element.addEventListener('click', () => {
      navigateToRelatedContent(notification);
    });
  }

  return element;
}
```

#### **Notification Icon Mapping:**
```javascript
function getNotificationIcon(type) {
  const iconMap = {
    'test_available': 'fas fa-clipboard-list text-blue',
    'test_deadline': 'fas fa-exclamation-triangle text-red',
    'exercise_graded': 'fas fa-star text-green',
    'coursework_graded': 'fas fa-award text-gold',
    'content_update': 'fas fa-book text-purple',
    'course_enrollment': 'fas fa-user-plus text-blue'
  };

  return iconMap[type] || 'fas fa-bell text-gray';
}
```

### **Mark as Read Functionality:**
```javascript
async function markAsRead(notificationId) {
  try {
    const res = await fetch('http://localhost:5000/mark-notification-read', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({ notificationId })
    });

    const data = await res.json();
    if (data.success) {
      // Update local notification state
      const notification = notifications.find(n => n._id === notificationId);
      if (notification) {
        notification.isRead = true;
        updateNotificationDisplay();
      }

      showSuccessMessage('Известието е маркирано като прочетено');
    }
  } catch (err) {
    console.error('Error marking notification as read:', err);
    showErrorMessage('Грешка при маркиране на известието');
  }
}

async function markAllAsRead() {
  try {
    const res = await fetch('http://localhost:5000/mark-all-notifications-read', {
      method: 'POST',
      credentials: 'include'
    });

    const data = await res.json();
    if (data.success) {
      // Update all local notifications
      notifications.forEach(n => n.isRead = true);
      updateNotificationDisplay();

      showSuccessMessage(`${data.updatedCount} известия са маркирани като прочетени`);
    }
  } catch (err) {
    console.error('Error marking all notifications as read:', err);
  }
}
```

### **Navigation to Related Content:**
```javascript
function navigateToRelatedContent(notification) {
  const { type, relatedEntityId, courseName, metadata } = notification;

  switch (type) {
    case 'test_available':
    case 'test_deadline':
      // Navigate to test taking page
      window.location.href = `/student/test.html?testId=${relatedEntityId}&course=${encodeURIComponent(courseName)}`;
      break;

    case 'exercise_graded':
    case 'coursework_graded':
      // Navigate to course page
      window.location.href = `/student/courses/courses_unlocked/unlocked_course_template.html?course=${encodeURIComponent(courseName)}`;
      break;

    case 'content_update':
      // Navigate to course materials
      window.location.href = `/student/courses/courses_unlocked/unlocked_course_template.html?course=${encodeURIComponent(courseName)}&tab=lectures`;
      break;

    case 'course_enrollment':
      // Navigate to my courses
      window.location.href = '/student/my-courses.html';
      break;

    default:
      // Default navigation to dashboard
      window.location.href = '/student/dashboard.html';
  }

  // Mark as read when navigating
  if (!notification.isRead) {
    markAsRead(notification._id);
  }
}
```

---

## 👨‍🏫 **TEACHER NOTIFICATION ФУНКЦИОНАЛНОСТИ** {#teacher-notifications}

### **Типове Teacher Notifications:**

#### **1. Exercise Submitted Notifications:**
**Trigger Event:** Студент предава упражнение
**Creation Logic:**
```javascript
// При успешно качване на упражнение
async function createExerciseSubmittedNotification(submissionData) {
  const course = await Course.findOne({ name: submissionData.courseName });
  const teacherEmail = course.teacher;

  const notification = new Notification({
    userEmail: teacherEmail,
    type: 'exercise_submitted',
    title: 'Ново предадено упражнение',
    message: `${submissionData.studentName} предаде упражнение ${submissionData.exerciseIndex} за курс ${submissionData.courseName}`,
    courseName: submissionData.courseName,
    relatedEntityId: submissionData.submissionId,
    metadata: {
      studentEmail: submissionData.studentEmail,
      studentName: submissionData.studentName,
      exerciseIndex: submissionData.exerciseIndex,
      fileName: submissionData.fileName,
      submittedAt: new Date()
    }
  });

  await notification.save();
}
```

#### **2. Test Completed Notifications:**
**Trigger Event:** Студент завършва тест
**Creation Logic:**
```javascript
// При подаване на тест
async function createTestCompletedNotification(testSubmission) {
  const test = await Test.findById(testSubmission.testId);
  const course = await Course.findOne({ name: test.courseName });
  const student = await User.findOne({ email: testSubmission.studentEmail });

  const notification = new Notification({
    userEmail: course.teacher,
    type: 'test_completed',
    title: 'Студент завърши тест',
    message: `${student.firstName} ${student.lastName} завърши тест "${test.title}" с резултат ${testSubmission.score}/${testSubmission.maxScore}`,
    courseName: test.courseName,
    relatedEntityId: testSubmission._id,
    metadata: {
      studentEmail: testSubmission.studentEmail,
      studentName: `${student.firstName} ${student.lastName}`,
      testTitle: test.title,
      score: testSubmission.score,
      maxScore: testSubmission.maxScore,
      timeSpent: testSubmission.timeSpent,
      autoSubmitted: testSubmission.autoSubmitted
    }
  });

  await notification.save();
}
```

#### **3. Course Enrollment Notifications:**
**Trigger Event:** Нов студент се записва в курс
**Creation Logic:**
```javascript
// При записване в курс
async function createCourseEnrollmentNotification(enrollmentData) {
  const course = await Course.findOne({ name: enrollmentData.courseName });
  const student = await User.findOne({ email: enrollmentData.studentEmail });

  const notification = new Notification({
    userEmail: course.teacher,
    type: 'course_enrollment',
    title: 'Нов студент в курса',
    message: `${student.firstName} ${student.lastName} се записа в курс "${enrollmentData.courseName}"`,
    courseName: enrollmentData.courseName,
    relatedEntityId: enrollmentData.studentEmail,
    metadata: {
      studentEmail: enrollmentData.studentEmail,
      studentName: `${student.firstName} ${student.lastName}`,
      enrolledAt: new Date()
    }
  });

  await notification.save();
}
```

### **Batch Notification Creation:**

#### **Notify All Students in Course:**
```javascript
// Helper function за batch notifications
async function notifyAllStudentsInCourse(courseName, notificationType, title, message, metadata = {}) {
  try {
    const course = await Course.findOne({ name: courseName });
    if (!course) {
      console.error('Course not found:', courseName);
      return;
    }

    // Create notifications for all enrolled students
    const notifications = course.accessors.map(studentEmail => ({
      userEmail: studentEmail,
      type: notificationType,
      title,
      message,
      courseName,
      relatedEntityId: metadata.relatedEntityId || '',
      isUrgent: metadata.isUrgent || false,
      metadata: metadata,
      createdAt: new Date()
    }));

    // Batch insert for performance
    const result = await Notification.insertMany(notifications);
    console.log(`✅ Created ${result.length} notifications for course ${courseName}`);

    return result;
  } catch (err) {
    console.error('❌ Error creating batch notifications:', err);
    throw err;
  }
}

// Usage examples
await notifyAllStudentsInCourse(
  'JavaScript Fundamentals',
  'content_update',
  'Ново съдържание добавено',
  'Добавени са нови лекции към курса',
  {
    relatedEntityId: courseId,
    contentType: 'lectures',
    addedCount: 3
  }
);

await notifyAllStudentsInCourse(
  'Database Systems',
  'test_available',
  'Нов тест достъпен',
  'Тест "SQL Queries" е достъпен за взимане',
  {
    relatedEntityId: testId,
    testStartDate: startDate,
    testEndDate: endDate,
    isUrgent: false
  }
);
```

### **Teacher Notification Management:**

#### **Administrative Notification Dashboard:**
```javascript
// Teacher-specific notification management
async function getTeacherNotificationSummary(teacherEmail) {
  try {
    const pipeline = [
      { $match: { userEmail: teacherEmail } },
      {
        $group: {
          _id: '$type',
          count: { $sum: 1 },
          unreadCount: {
            $sum: { $cond: [{ $eq: ['$isRead', false] }, 1, 0] }
          },
          latestNotification: { $max: '$createdAt' }
        }
      },
      { $sort: { latestNotification: -1 } }
    ];

    const summary = await Notification.aggregate(pipeline);

    return {
      totalNotifications: summary.reduce((sum, item) => sum + item.count, 0),
      totalUnread: summary.reduce((sum, item) => sum + item.unreadCount, 0),
      byType: summary
    };
  } catch (err) {
    console.error('Error getting teacher notification summary:', err);
    throw err;
  }
}
```

#### **Bulk Actions for Teachers:**
```javascript
// Mark notifications by type as read
async function markNotificationsByTypeAsRead(teacherEmail, notificationType) {
  try {
    const result = await Notification.updateMany(
      {
        userEmail: teacherEmail,
        type: notificationType,
        isRead: false
      },
      {
        $set: {
          isRead: true,
          readAt: new Date()
        }
      }
    );

    return {
      success: true,
      updatedCount: result.modifiedCount,
      message: `Маркирани са ${result.modifiedCount} известия от тип ${notificationType}`
    };
  } catch (err) {
    console.error('Error marking notifications by type:', err);
    throw err;
  }
}

// Delete old notifications (cleanup)
async function cleanupOldNotifications(daysOld = 30) {
  try {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - daysOld);

    const result = await Notification.deleteMany({
      createdAt: { $lt: cutoffDate },
      isRead: true
    });

    console.log(`🧹 Cleaned up ${result.deletedCount} old notifications`);
    return result.deletedCount;
  } catch (err) {
    console.error('Error cleaning up notifications:', err);
    throw err;
  }
}
```

### **Teacher Frontend Implementation:**

#### **Enhanced Notification Display:**
```javascript
// Teacher-specific notification enhancements
function createTeacherNotificationElement(notification) {
  const element = document.createElement('div');
  element.className = `notification-item teacher-notification ${!notification.isRead ? 'unread' : ''} ${notification.isUrgent ? 'urgent' : ''}`;

  const timeAgo = getTimeAgo(new Date(notification.createdAt));
  const studentInfo = notification.metadata.studentName || notification.metadata.studentEmail || '';

  element.innerHTML = `
    <div class="notification-icon">
      <i class="${getTeacherNotificationIcon(notification.type)}"></i>
    </div>
    <div class="notification-content">
      <div class="notification-header">
        <h4>${notification.title}</h4>
        <span class="notification-time">${timeAgo}</span>
      </div>
      <p class="notification-message">${notification.message}</p>
      <div class="notification-meta">
        ${notification.courseName ? `<span class="course-badge">${notification.courseName}</span>` : ''}
        ${studentInfo ? `<span class="student-badge">${studentInfo}</span>` : ''}
      </div>
    </div>
    <div class="notification-actions">
      <button class="action-btn primary" onclick="handleTeacherNotificationAction('${notification._id}', '${notification.type}')">
        ${getActionButtonText(notification.type)}
      </button>
      ${!notification.isRead ? '<button class="action-btn secondary" onclick="markAsRead(\'' + notification._id + '\')">Маркирай</button>' : ''}
    </div>
  `;

  return element;
}

function getTeacherNotificationIcon(type) {
  const iconMap = {
    'exercise_submitted': 'fas fa-file-upload text-blue',
    'test_completed': 'fas fa-check-circle text-green',
    'course_enrollment': 'fas fa-user-plus text-purple',
    'content_update': 'fas fa-upload text-orange'
  };

  return iconMap[type] || 'fas fa-bell text-gray';
}

function getActionButtonText(type) {
  const actionMap = {
    'exercise_submitted': 'Прегледай',
    'test_completed': 'Виж резултат',
    'course_enrollment': 'Виж студент',
    'content_update': 'Управлявай'
  };

  return actionMap[type] || 'Отвори';
}

function handleTeacherNotificationAction(notificationId, type) {
  const notification = notifications.find(n => n._id === notificationId);
  if (!notification) return;

  switch (type) {
    case 'exercise_submitted':
      // Navigate to teacher dashboard for grading
      window.location.href = `/teacher/teacher-dashboard.html?course=${encodeURIComponent(notification.courseName)}&student=${notification.metadata.studentEmail}&tab=exercises`;
      break;

    case 'test_completed':
      // Navigate to test results
      window.location.href = `/teacher/test-results.html?testId=${notification.relatedEntityId}&student=${notification.metadata.studentEmail}`;
      break;

    case 'course_enrollment':
      // Navigate to course management
      window.location.href = `/teacher/teacher-dashboard.html?course=${encodeURIComponent(notification.courseName)}`;
      break;

    case 'content_update':
      // Navigate to course content management
      window.location.href = `/teacher/courses/courses_unlocked/teacher_unlocked_course_template.html?course=${encodeURIComponent(notification.courseName)}`;
      break;
  }

  // Mark as read when taking action
  if (!notification.isRead) {
    markAsRead(notificationId);
  }
}
```

---

## 🔧 **TECHNICAL IMPLEMENTATION DETAILS** {#technical-implementation}

### **JavaScript Notification Polling System:**

#### **Core Polling Implementation:**
```javascript
// notification-system.js - Core polling mechanism
class NotificationSystem {
  constructor(options = {}) {
    this.pollInterval = options.pollInterval || 30000; // 30 seconds
    this.maxRetries = options.maxRetries || 3;
    this.retryDelay = options.retryDelay || 5000; // 5 seconds
    this.isPolling = false;
    this.retryCount = 0;
    this.notifications = [];
    this.unreadCount = 0;
    this.lastPollTime = null;

    // Bind methods
    this.poll = this.poll.bind(this);
    this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
  }

  async initialize() {
    try {
      // Initial load
      await this.loadNotifications();

      // Start polling
      this.startPolling();

      // Handle page visibility changes
      document.addEventListener('visibilitychange', this.handleVisibilityChange);

      // Handle window focus/blur
      window.addEventListener('focus', () => this.onWindowFocus());
      window.addEventListener('blur', () => this.onWindowBlur());

      console.log('✅ Notification system initialized');
    } catch (err) {
      console.error('❌ Failed to initialize notification system:', err);
    }
  }

  startPolling() {
    if (this.isPolling) return;

    this.isPolling = true;
    this.pollTimer = setInterval(this.poll, this.pollInterval);
    console.log(`🔄 Started notification polling (${this.pollInterval}ms interval)`);
  }

  stopPolling() {
    if (!this.isPolling) return;

    this.isPolling = false;
    if (this.pollTimer) {
      clearInterval(this.pollTimer);
      this.pollTimer = null;
    }
    console.log('⏹️ Stopped notification polling');
  }

  async poll() {
    try {
      await this.loadNotifications();
      this.retryCount = 0; // Reset retry count on success
    } catch (err) {
      console.error('Polling error:', err);
      this.handlePollError(err);
    }
  }

  async loadNotifications() {
    const startTime = Date.now();

    try {
      const response = await fetch('http://localhost:5000/get-notifications', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          limit: 50,
          lastPollTime: this.lastPollTime
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.success) {
        const oldUnreadCount = this.unreadCount;
        this.notifications = data.notifications || [];
        this.unreadCount = data.unreadCount || 0;
        this.lastPollTime = new Date().toISOString();

        // Update UI
        this.updateNotificationDisplay();

        // Check for new notifications
        if (this.unreadCount > oldUnreadCount) {
          this.handleNewNotifications(this.unreadCount - oldUnreadCount);
        }

        // Performance logging
        const loadTime = Date.now() - startTime;
        if (loadTime > 1000) {
          console.warn(`⚠️ Slow notification load: ${loadTime}ms`);
        }
      } else {
        throw new Error(data.message || 'Failed to load notifications');
      }
    } catch (err) {
      console.error('Error loading notifications:', err);
      throw err;
    }
  }

  handlePollError(error) {
    this.retryCount++;

    if (this.retryCount >= this.maxRetries) {
      console.error(`❌ Max retries (${this.maxRetries}) reached. Stopping polling.`);
      this.stopPolling();
      this.showConnectionError();
      return;
    }

    console.warn(`⚠️ Polling failed (attempt ${this.retryCount}/${this.maxRetries}). Retrying in ${this.retryDelay}ms...`);

    // Exponential backoff
    const delay = this.retryDelay * Math.pow(2, this.retryCount - 1);
    setTimeout(() => {
      if (this.isPolling) {
        this.poll();
      }
    }, delay);
  }

  handleVisibilityChange() {
    if (document.hidden) {
      // Page is hidden - reduce polling frequency
      this.stopPolling();
      this.startPolling(60000); // Poll every minute when hidden
    } else {
      // Page is visible - resume normal polling
      this.stopPolling();
      this.startPolling();
      // Immediate poll when page becomes visible
      this.poll();
    }
  }

  onWindowFocus() {
    // Immediate poll when window gains focus
    if (this.isPolling) {
      this.poll();
    }
  }

  onWindowBlur() {
    // Optional: reduce polling when window loses focus
  }

  handleNewNotifications(newCount) {
    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
      new Notification(`${newCount} нови известия`, {
        body: 'Кликнете за преглед',
        icon: '/images/notification-icon.png',
        tag: 'student-system-notification'
      });
    }

    // Play notification sound (optional)
    this.playNotificationSound();

    // Highlight notification bell
    this.highlightNotificationBell();
  }

  playNotificationSound() {
    try {
      const audio = new Audio('/sounds/notification.mp3');
      audio.volume = 0.3;
      audio.play().catch(err => {
        // Ignore audio play errors (user interaction required)
      });
    } catch (err) {
      // Audio not available
    }
  }

  highlightNotificationBell() {
    const bell = document.getElementById('notificationBell');
    if (bell) {
      bell.classList.add('notification-pulse');
      setTimeout(() => {
        bell.classList.remove('notification-pulse');
      }, 2000);
    }
  }

  showConnectionError() {
    const errorElement = document.getElementById('connectionError');
    if (errorElement) {
      errorElement.style.display = 'block';
      errorElement.innerHTML = `
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-triangle"></i>
          Проблем с връзката. Известията може да не са актуални.
          <button onclick="notificationSystem.reconnect()" class="btn btn-sm btn-primary">Опитай отново</button>
        </div>
      `;
    }
  }

  async reconnect() {
    this.retryCount = 0;
    const errorElement = document.getElementById('connectionError');
    if (errorElement) {
      errorElement.style.display = 'none';
    }

    try {
      await this.loadNotifications();
      this.startPolling();
      this.showSuccessMessage('Връзката е възстановена');
    } catch (err) {
      this.showErrorMessage('Неуспешно възстановяване на връзката');
    }
  }

  updateNotificationDisplay() {
    this.updateNotificationBell();
    this.updateNotificationList();
    this.updateNotificationBadges();
  }

  updateNotificationBell() {
    const bell = document.getElementById('notificationBell');
    const dot = document.getElementById('notificationDot');

    if (dot) {
      if (this.unreadCount > 0) {
        dot.style.display = 'block';
        dot.textContent = this.unreadCount > 99 ? '99+' : this.unreadCount;
        dot.className = this.hasUrgentNotifications() ? 'notification-dot urgent' : 'notification-dot';
      } else {
        dot.style.display = 'none';
      }
    }

    if (bell) {
      bell.className = this.unreadCount > 0 ? 'notification-bell has-notifications' : 'notification-bell';
    }
  }

  hasUrgentNotifications() {
    return this.notifications.some(n => n.isUrgent && !n.isRead);
  }

  destroy() {
    this.stopPolling();
    document.removeEventListener('visibilitychange', this.handleVisibilityChange);
    window.removeEventListener('focus', this.onWindowFocus);
    window.removeEventListener('blur', this.onWindowBlur);
    console.log('🗑️ Notification system destroyed');
  }
}

// Global instance
let notificationSystem;

// Initialize on page load
document.addEventListener('DOMContentLoaded', () => {
  notificationSystem = new NotificationSystem({
    pollInterval: 30000,
    maxRetries: 3,
    retryDelay: 5000
  });

  notificationSystem.initialize();
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
  if (notificationSystem) {
    notificationSystem.destroy();
  }
});
```

### **Urgent Notifications Handling:**

#### **Priority System Implementation:**
```javascript
// urgent-notifications.js
class UrgentNotificationHandler {
  constructor() {
    this.urgentQueue = [];
    this.isShowingUrgent = false;
  }

  handleUrgentNotification(notification) {
    if (!notification.isUrgent) return;

    this.urgentQueue.push(notification);

    if (!this.isShowingUrgent) {
      this.showNextUrgentNotification();
    }
  }

  showNextUrgentNotification() {
    if (this.urgentQueue.length === 0) {
      this.isShowingUrgent = false;
      return;
    }

    this.isShowingUrgent = true;
    const notification = this.urgentQueue.shift();

    this.createUrgentModal(notification);
  }

  createUrgentModal(notification) {
    // Remove existing urgent modal
    const existingModal = document.getElementById('urgentNotificationModal');
    if (existingModal) {
      existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.id = 'urgentNotificationModal';
    modal.className = 'urgent-notification-modal';

    modal.innerHTML = `
      <div class="urgent-modal-overlay"></div>
      <div class="urgent-modal-content">
        <div class="urgent-modal-header">
          <i class="fas fa-exclamation-triangle urgent-icon"></i>
          <h3>СПЕШНО ИЗВЕСТИЕ</h3>
        </div>
        <div class="urgent-modal-body">
          <h4>${notification.title}</h4>
          <p>${notification.message}</p>
          ${notification.metadata.timeRemaining ?
            `<div class="time-remaining">
              <i class="fas fa-clock"></i>
              Оставащо време: <span class="countdown">${notification.metadata.timeRemaining}</span>
            </div>` : ''
          }
        </div>
        <div class="urgent-modal-actions">
          <button class="btn btn-primary" onclick="urgentHandler.handleUrgentAction('${notification._id}', '${notification.type}')">
            ${this.getUrgentActionText(notification.type)}
          </button>
          <button class="btn btn-secondary" onclick="urgentHandler.dismissUrgent('${notification._id}')">
            Затвори
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(modal);

    // Auto-dismiss after 30 seconds
    setTimeout(() => {
      this.dismissUrgent(notification._id);
    }, 30000);

    // Start countdown if applicable
    if (notification.metadata.timeRemaining) {
      this.startCountdown(notification);
    }
  }

  getUrgentActionText(type) {
    const actionMap = {
      'test_deadline': 'Започни тест',
      'test_available': 'Виж тест',
      'exercise_graded': 'Виж оценка',
      'content_update': 'Виж материали'
    };

    return actionMap[type] || 'Отвори';
  }

  handleUrgentAction(notificationId, type) {
    const notification = notificationSystem.notifications.find(n => n._id === notificationId);
    if (notification) {
      navigateToRelatedContent(notification);
    }

    this.dismissUrgent(notificationId);
  }

  dismissUrgent(notificationId) {
    const modal = document.getElementById('urgentNotificationModal');
    if (modal) {
      modal.remove();
    }

    // Mark as read
    markAsRead(notificationId);

    // Show next urgent notification
    setTimeout(() => {
      this.showNextUrgentNotification();
    }, 500);
  }

  startCountdown(notification) {
    const countdownElement = document.querySelector('.countdown');
    if (!countdownElement) return;

    const updateCountdown = () => {
      const timeRemaining = calculateTimeRemaining(notification.metadata.dueDate);
      countdownElement.textContent = timeRemaining;

      if (timeRemaining === '00:00:00') {
        countdownElement.textContent = 'ИЗТЕКЪЛ';
        countdownElement.className = 'countdown expired';
      }
    };

    // Update every second
    const countdownTimer = setInterval(updateCountdown, 1000);

    // Clear timer when modal is dismissed
    const modal = document.getElementById('urgentNotificationModal');
    if (modal) {
      modal.addEventListener('remove', () => {
        clearInterval(countdownTimer);
      });
    }
  }
}

// Global urgent handler
const urgentHandler = new UrgentNotificationHandler();
```

### **Performance Optimization:**

#### **Notification Caching System:**
```javascript
// notification-cache.js
class NotificationCache {
  constructor() {
    this.cache = new Map();
    this.maxCacheSize = 100;
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
  }

  set(key, data) {
    // Remove oldest entries if cache is full
    if (this.cache.size >= this.maxCacheSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  get(key) {
    const cached = this.cache.get(key);
    if (!cached) return null;

    // Check if cache is expired
    if (Date.now() - cached.timestamp > this.cacheTimeout) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  clear() {
    this.cache.clear();
  }

  cleanup() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.cacheTimeout) {
        this.cache.delete(key);
      }
    }
  }
}

// Global cache instance
const notificationCache = new NotificationCache();

// Cleanup expired cache entries every 5 minutes
setInterval(() => {
  notificationCache.cleanup();
}, 5 * 60 * 1000);
```

---

## 📊 **WORKFLOW ДИАГРАМИ** {#workflow-диаграми}

### **Notification Creation Workflow:**

#### **Backend Event → Frontend Display Flow:**
```mermaid
graph TD
    A[User Action/System Event] --> B{Event Type}

    B -->|Student submits exercise| C[Exercise Submitted Event]
    B -->|Teacher activates test| D[Test Available Event]
    B -->|Test deadline approaching| E[Test Deadline Event]
    B -->|Grade assigned| F[Grade Assigned Event]

    C --> G[Create Teacher Notification]
    D --> H[Create Student Notifications]
    E --> I[Create Urgent Student Notifications]
    F --> J[Create Student Notification]

    G --> K[Save to MongoDB]
    H --> K
    I --> K
    J --> K

    K --> L[Client Polling Request]
    L --> M[API Response with Notifications]
    M --> N[Frontend Processing]

    N --> O{Notification Type}
    O -->|Regular| P[Update Notification Bell]
    O -->|Urgent| Q[Show Urgent Modal]

    P --> R[Update Dropdown List]
    Q --> R
    R --> S[User Sees Notification]
```

### **Student Notification Interaction Flow:**

#### **Student User Journey:**
```mermaid
sequenceDiagram
    participant S as Student
    participant F as Frontend
    participant A as API
    participant D as Database
    participant T as Teacher

    Note over S,T: Test Available Notification Flow

    T->>A: Activate Test
    A->>D: Create Notifications for All Students

    loop Every 30 seconds
        F->>A: Poll for notifications
        A->>D: Query notifications
        D->>A: Return notifications
        A->>F: Send notification data
    end

    F->>F: Update notification bell (red dot)
    F->>S: Show notification in dropdown

    S->>F: Click on notification
    F->>F: Navigate to test page
    F->>A: Mark notification as read
    A->>D: Update isRead = true

    S->>F: Take test
    F->>A: Submit test
    A->>D: Save test submission
    A->>D: Create teacher notification
```

### **Teacher Notification Management Flow:**

#### **Teacher Administrative Workflow:**
```mermaid
flowchart TD
    A[Teacher Dashboard] --> B{Notification Action}

    B -->|View Submissions| C[Student Submissions Notifications]
    B -->|Check Test Results| D[Test Completion Notifications]
    B -->|Manage Course| E[Course Enrollment Notifications]

    C --> F[Filter by Course]
    D --> G[Filter by Test]
    E --> H[Filter by Date]

    F --> I[Grade Submissions]
    G --> J[Analyze Results]
    H --> K[Approve Enrollments]

    I --> L[Create Grade Notifications for Students]
    J --> M[Export Results]
    K --> N[Send Welcome Notifications]

    L --> O[Batch Notification Creation]
    M --> P[Generate Reports]
    N --> O

    O --> Q[Update Database]
    Q --> R[Students Receive Notifications]
```

### **Real-time Polling System Architecture:**

#### **Client-Server Communication Pattern:**
```mermaid
graph LR
    subgraph "Client Side"
        A[Page Load] --> B[Initialize Notification System]
        B --> C[Start 30s Interval Timer]
        C --> D[Make API Request]
        D --> E{Response OK?}
        E -->|Yes| F[Update UI]
        E -->|No| G[Retry Logic]
        G --> H{Max Retries?}
        H -->|No| I[Exponential Backoff]
        H -->|Yes| J[Stop Polling]
        I --> D
        F --> K[Wait 30 seconds]
        K --> D
    end

    subgraph "Server Side"
        L[Receive Request] --> M[Authenticate User]
        M --> N[Query Database]
        N --> O[Filter by User Role]
        O --> P[Sort by Priority]
        P --> Q[Return JSON Response]
    end

    D --> L
    Q --> E
```

### **Urgent Notification Priority System:**

#### **Urgent Notification Handling Flow:**
```mermaid
stateDiagram-v2
    [*] --> Polling

    Polling --> NewNotifications : API Response
    NewNotifications --> CheckUrgency : Process Notifications

    CheckUrgency --> RegularNotification : isUrgent = false
    CheckUrgency --> UrgentNotification : isUrgent = true

    RegularNotification --> UpdateBell : Add to dropdown
    UrgentNotification --> UrgentQueue : Add to urgent queue

    UrgentQueue --> ShowModal : Display urgent modal
    ShowModal --> UserAction : Wait for user interaction

    UserAction --> TakeAction : User clicks action button
    UserAction --> Dismiss : User dismisses modal
    UserAction --> AutoDismiss : 30 second timeout

    TakeAction --> Navigate : Redirect to related content
    Dismiss --> MarkRead : Mark as read
    AutoDismiss --> MarkRead

    Navigate --> MarkRead
    MarkRead --> CheckQueue : Check for more urgent notifications

    CheckQueue --> ShowModal : More urgent notifications
    CheckQueue --> UpdateBell : No more urgent notifications

    UpdateBell --> Polling : Continue polling
```

### **Database Notification Lifecycle:**

#### **Notification Data Flow:**
```mermaid
erDiagram
    USER ||--o{ NOTIFICATION : receives
    COURSE ||--o{ NOTIFICATION : relates_to
    TEST ||--o{ NOTIFICATION : triggers
    SUBMISSION ||--o{ NOTIFICATION : creates

    NOTIFICATION {
        string userEmail
        string type
        string title
        string message
        string courseName
        string relatedEntityId
        boolean isRead
        boolean isUrgent
        object metadata
        date expiresAt
        date createdAt
    }

    USER {
        string email
        string role
        string firstName
        string lastName
    }

    COURSE {
        string name
        string teacher
        array accessors
    }

    TEST {
        string title
        string courseName
        date startDate
        date endDate
        boolean isActive
    }

    SUBMISSION {
        string studentEmail
        string testId
        number score
        boolean isCompleted
    }
```

### **Performance Optimization Flow:**

#### **Caching and Optimization Strategy:**
```mermaid
graph TB
    A[Client Request] --> B{Cache Hit?}
    B -->|Yes| C[Return Cached Data]
    B -->|No| D[Query Database]

    D --> E[Apply Filters]
    E --> F[Sort by Priority]
    F --> G[Limit Results]
    G --> H[Cache Response]
    H --> I[Return to Client]

    C --> J[Update UI]
    I --> J

    J --> K{New Notifications?}
    K -->|Yes| L[Show Browser Notification]
    K -->|No| M[Update Bell Only]

    L --> N[Play Sound]
    N --> O[Highlight Bell]
    O --> P[Update Dropdown]

    M --> P
    P --> Q[Schedule Next Poll]

    subgraph "Background Tasks"
        R[Cleanup Old Notifications]
        S[Update Urgent Status]
        T[Expire Notifications]
    end

    Q --> R
    R --> S
    S --> T
    T --> A
```

---

## 📋 **ЗАКЛЮЧЕНИЕ**

### **Notification System Highlights:**

#### **Архитектурни постижения:**
- ✅ **Real-time Communication** - 30-second polling с intelligent retry logic
- ✅ **Role-based Notifications** - Специализирани известия за студенти и учители
- ✅ **Urgent Priority System** - Критични известия с modal display
- ✅ **Performance Optimization** - Caching, batching, и efficient database queries
- ✅ **User Experience** - Intuitive UI с browser notifications и sound alerts

#### **Technical Features:**
- **Scalable Architecture** - Поддържа хиляди едновременни потребители
- **Fault Tolerance** - Graceful degradation при network issues
- **Battery Optimization** - Intelligent polling frequency adjustment
- **Accessibility** - Screen reader support и keyboard navigation
- **Cross-browser Compatibility** - Работи на всички модерни браузъри

#### **Business Value:**
- **Enhanced Engagement** - Студентите са винаги информирани за важни събития
- **Improved Efficiency** - Учителите получават prompt notifications за student activity
- **Better Communication** - Автоматизирана комуникация между всички участници
- **Reduced Missed Deadlines** - Urgent notifications за приближаващи крайни срокове

### **Production Readiness:**
Notification системата представлява **enterprise-grade solution** с comprehensive error handling, performance optimization, и user-centric design подходящ за educational technology platform в production environment.

---

*Тази comprehensive документация покрива всички аспекти на notification системата в студентската система за управление, от техническа архитектура до user experience patterns.*
