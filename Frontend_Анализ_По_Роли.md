# Frontend Архитектура - Студентска Система за Управление
## Comprehensive анализ по роли на български език

---

## 📋 **СЪДЪРЖАНИЕ**

1. [Общ преглед на frontend структурата](#общ-преглед)
2. [Студентска роля - `/public/student/`](#студентска-роля)
3. [Учителска роля - `/public/teacher/`](#учителска-роля)
4. [Споделени компоненти - `/public/`](#споделени-компоненти)
5. [Технически детайли и patterns](#технически-детайли)
6. [Role-based UI различия](#role-based-различия)
7. [Integration с backend](#backend-integration)

---

## 🏗️ **ОБЩ ПРЕГЛЕД НА FRONTEND СТРУКТУРАТА** {#общ-преглед}

### **Архитектурен подход:**
Студентската система използва **role-based directory separation** с пълно разделение между студентски и учителски интерфейси:

```
public/
├── index.html (споделена начална страница)
├── login.html (споделена)
├── registration.html (споделена)
├── css/
│   └── style.css (основни стилове)
├── student/ (пълна студентска функционалност)
│   ├── *.html (студентски страници)
│   ├── css/ (студентски стилове)
│   ├── js/ (студентски JavaScript модули)
│   └── images/ (студентски ресурси)
└── teacher/ (пълна учителска функционалност)
    ├── *.html (учителски страници)
    ├── css/ (учителски стилове)
    ├── js/ (учителски JavaScript модули)
    └── images/ (учителски ресурси)
```

### **Ключови характеристики:**
- **Complete separation** между роли
- **ES6+ modules** за модулна архитектура
- **Responsive design** с mobile-first подход
- **Real-time notifications** с 30-second polling
- **JWT authentication** с cookie handling
- **File upload workflows** с Cloudinary integration

---

## 👨‍🎓 **СТУДЕНТСКА РОЛЯ - `/public/student/`** {#студентска-роля}

### **HTML страници и функционалност:**

#### **Основни страници:**
1. **dashboard.html** - Главно табло с feature cards
2. **my-courses.html** - Записани курсове с grid/list view
3. **my-tasks.html** - Задачи организирани по категории (urgent/completed/upcoming)
4. **my-progress.html** - Детайлен прогрес с charts и statistics
5. **courses.html** - Всички курсове с enrollment functionality
6. **profile.html** - Профил с avatar upload и settings
7. **test.html** - Test taking interface с countdown timer

#### **Course templates:**
8. **courses/courses_locked/** - Template за заключени курсове
9. **courses/courses_unlocked/** - Template за отключени курсове с content access

### **CSS архитектура:**

#### **Основни стилове:**
```css
/* CSS Variables за consistency */
:root {
  --primary-color: #44749d;
  --primary-dark: #345d7e;
  --secondary-color: #1abc9c;
  --text-color: #2c3e50;
  --light-gray: #ecf0f1;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}
```

#### **Файлове:**
- **css/student-pages.css** - Основни стилове за всички студентски страници
- **css/dashboard_style.css** - Специфични стилове за dashboard
- **css/courses_style.css** - Course grid и card стилове
- **css/profile_style.css** - Profile page стилове
- **css/login_style.css** - Login/registration стилове

#### **Responsive design patterns:**
```css
/* Mobile-first approach */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .courses-grid {
    grid-template-columns: 1fr;
    padding: 1rem;
  }
  
  .container {
    flex-direction: column;
  }
  
  aside {
    width: 100%;
    text-align: center;
  }
}
```

### **JavaScript модули и роля:**

#### **Core модули:**
1. **auth.js** - Authentication handling с JWT cookies
2. **notifications.js** - Real-time notification система
3. **profile-menu.js** - Profile dropdown functionality
4. **sidebar-courses.js** - Dynamic sidebar course loading
5. **enroll-hover.js** - Course enrollment hover panel

#### **Page-specific модули:**
6. **dashboard-init.js** - Dashboard initialization
7. **my-courses.js** - Course management functionality
8. **my-tasks.js** - Task filtering и organization
9. **my-progress.js** - Progress calculations и charts
10. **test-taking.js** - Test interface с timer functionality
11. **unlocked-course-core.js** - Course content management

#### **Utility модули:**
12. **test-utils.js** - Test status calculations и Bulgarian date formatting
13. **courses-core.js** - Course loading и display logic

### **Aside menu структура:**
```html
<aside>
  <div class="logo">
    <img src="images/logo1.png" alt="Лого на Студентска Система">
  </div>
  <ul>
    <li><a href="dashboard.html"><i class="fas fa-home"></i> Начало</a></li>
    <li><a href="my-courses.html"><i class="fas fa-book-open"></i> Моите Курсове</a></li>
    <li><a href="my-tasks.html"><i class="fas fa-tasks"></i> Задачи</a></li>
    <li><a href="my-progress.html"><i class="fas fa-chart-bar"></i> Прогрес</a></li>
    <li class="courses-hover">
      <a href="courses.html"><i class="fas fa-book"></i> Всички курсове</a>
      <div class="hover-panel">
        <form id="hoverEnrollForm">
          <input type="text" id="hoverCourseCode" placeholder="Код за включване" required>
          <button type="submit">Включи</button>
        </form>
      </div>
    </li>
  </ul>
  <hr class="divider">
  <h4 class="my-courses-title">Моите Курсове</h4>
  <ul id="myCoursesList" class="my-courses-list"></ul>
</aside>
```

### **API интеграция patterns:**

#### **Authentication pattern:**
```javascript
// Consistent authentication check
export async function checkAuthAndRedirect() {
  try {
    const res = await fetch('http://localhost:5000/get-my-courses', {
      method: 'POST',
      credentials: 'include'
    });

    const data = await res.json();

    if (!res.ok || !data.success) {
      window.location.href = '/login.html';
      return null;
    }

    return { isLoggedIn: true };
  } catch (err) {
    window.location.href = '/login.html';
    return null;
  }
}
```

#### **API call pattern:**
```javascript
// Standard API call with error handling
async function loadMyCourses() {
  try {
    const res = await fetch('http://localhost:5000/get-my-courses', {
      method: 'POST',
      credentials: 'include'
    });

    const data = await res.json();
    if (data.success) {
      myCourses = data.courses;
      displayCourses();
    }
  } catch (err) {
    console.error('Error loading courses:', err);
    showErrorMessage('Грешка при зареждане на курсовете');
  }
}
```

### **File upload workflows:**

#### **Exercise upload pattern:**
```javascript
async function uploadExercise(file, courseName, exerciseIndex) {
  const formData = new FormData();
  formData.append('exercise', file);
  formData.append('courseName', courseName);
  formData.append('exerciseIndex', exerciseIndex);

  try {
    const res = await fetch('http://localhost:5000/upload-exercise', {
      method: 'POST',
      credentials: 'include',
      body: formData
    });

    const data = await res.json();
    if (data.success) {
      showSuccessMessage('Упражнението е качено успешно!');
      updateExerciseDisplay(data.url, data.originalName);
    }
  } catch (err) {
    showErrorMessage('Грешка при качване на файла');
  }
}
```

### **Real-time notification implementation:**

#### **Notification система:**
```javascript
// notifications.js
export function initializeNotifications() {
  setupNotificationBell();
  loadNotifications();
  
  // Real-time updates every 30 seconds
  setInterval(loadNotifications, 30000);
}

async function loadNotifications() {
  try {
    const res = await fetch('http://localhost:5000/get-notifications', {
      method: 'POST',
      credentials: 'include'
    });

    const data = await res.json();
    if (data.success) {
      notifications = data.notifications || [];
      updateNotificationDisplay();
    }
  } catch (err) {
    console.error('Error loading notifications:', err);
  }
}

function updateNotificationDisplay() {
  const notificationDot = document.getElementById('notificationDot');
  const notificationList = document.getElementById('notificationList');
  
  // Update unread count
  unreadCount = notifications.filter(n => !n.isRead).length;
  
  // Show/hide notification dot
  if (notificationDot) {
    notificationDot.style.display = unreadCount > 0 ? 'block' : 'none';
  }
  
  // Update notification list
  if (notifications.length === 0) {
    notificationList.innerHTML = `
      <div class="no-notifications">
        <i class="fas fa-bell-slash"></i>
        <p>Няма нови известия</p>
      </div>
    `;
  } else {
    notificationList.innerHTML = '';
    notifications.forEach(notification => {
      const notificationElement = createNotificationElement(notification);
      notificationList.appendChild(notificationElement);
    });
  }
}
```

---

## 👨‍🏫 **УЧИТЕЛСКА РОЛЯ - `/public/teacher/`** {#учителска-роля}

### **HTML страници и функционалност:**

#### **Основни страници:**
1. **dashboard.html** - Учителско главно табло
2. **courses.html** - Всички курсове с management capabilities
3. **teacher-dashboard.html** - Управление на студенти и оценки
4. **create-test.html** - Създаване на тестове с question builder
5. **my-scheduled-tests.html** - Планирани тестове с results viewing
6. **profile.html** - Учителски профил с settings

#### **Course management templates:**
7. **courses/courses_unlocked/** - Teacher course template с content management
8. **test-results.html** - Detailed test results с export functionality

### **CSS архитектура - Teacher-specific:**

#### **Teacher стилове:**
```css
/* Teacher-specific color scheme */
:root {
  --teacher-accent: #9b59b6;
  --teacher-accent-dark: #8e44ad;
  --teacher-primary: #667eea;
  --teacher-secondary: #764ba2;
}

/* Teacher dashboard header */
.teacher-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  border-radius: 12px;
  margin-bottom: 2rem;
  text-align: center;
}
```

#### **Файлове:**
- **css/student-pages.css** - Споделени стилове (копие от student)
- **css/teacher-dashboard.css** - Teacher-specific dashboard стилове
- **css/courses_style.css** - Course management стилове
- **css/profile_style.css** - Teacher profile стилове

### **JavaScript модули - Teacher-specific:**

#### **Core модули (идентични със student):**
1. **auth.js** - Authentication с същата логика
2. **notifications.js** - Teacher notification types
3. **profile-menu.js** - Profile dropdown
4. **sidebar-courses.js** - Course sidebar loading

#### **Teacher-specific модули:**
5. **dashboard-init.js** - Teacher dashboard initialization
6. **courses-init.js** - Course management initialization
7. **teacher-dashboard.js** - Student management functionality
8. **create-test.js** - Test creation interface
9. **test-results.js** - Results viewing и export

### **Aside menu структура - Teacher:**
```html
<aside>
  <div class="logo">
    <img src="images/logo1.png" alt="Лого на Студентска Система">
  </div>
  <ul>
    <li><a href="dashboard.html"><i class="fas fa-home"></i> Начало</a></li>
    <li><a href="courses.html"><i class="fas fa-book"></i> Всички курсове</a></li>
    <li><a href="teacher-dashboard.html"><i class="fas fa-chalkboard-teacher"></i> Учителско табло</a></li>
    <li><a href="create-test.html"><i class="fas fa-plus-circle"></i> Създай тест</a></li>
    <li><a href="my-scheduled-tests.html"><i class="fas fa-calendar-alt"></i> Моите планирани тестове</a></li>
  </ul>
  <hr class="divider">
  <h4 class="my-courses-title">Моите Курсове</h4>
  <ul id="myCoursesList" class="my-courses-list"></ul>
</aside>
```

### **Teacher API patterns:**

#### **Course creation pattern:**
```javascript
async function createCourse(courseData) {
  try {
    const res = await fetch('http://localhost:5000/create-course', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify(courseData)
    });

    const data = await res.json();
    if (data.success) {
      showSuccessMessage('Курсът е създаден успешно!');
      loadAllCourses(); // Refresh course list
    }
  } catch (err) {
    showErrorMessage('Грешка при създаване на курса');
  }
}
```

#### **Student grading pattern:**
```javascript
async function saveGrade(studentEmail, courseName, type, index, grade) {
  try {
    const res = await fetch('http://localhost:5000/save-grade', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({
        studentEmail,
        courseName,
        type,
        index,
        grade
      })
    });

    const data = await res.json();
    if (data.success) {
      showSuccessMessage('Оценката е запазена успешно!');
      updateGradeDisplay(grade);
    }
  } catch (err) {
    showErrorMessage('Грешка при запазване на оценката');
  }
}
```

---

## 🌐 **СПОДЕЛЕНИ КОМПОНЕНТИ - `/public/`** {#споделени-компоненти}

### **Споделени HTML страници:**
1. **index.html** - Landing page с role detection
2. **login.html** - Unified login form
3. **registration.html** - User registration

### **Споделени CSS:**
- **css/style.css** - Base стилове за layout, typography, responsive design

### **Споделени ресурси:**
- **images/** - Общи изображения и иконки
- **js/** - Utility scripts (ако има)

---

## 🔧 **ТЕХНИЧЕСКИ ДЕТАЙЛИ И PATTERNS** {#технически-детайли}

### **ES6+ Modules използване:**

#### **Module import pattern:**
```javascript
// Standard import pattern във всеки page init file
import { checkAuthAndRedirect } from './auth.js';
import { setupProfileDropdown } from './profile-menu.js';
import { setupEnrollHoverPanel } from './enroll-hover.js';
import { loadSidebarCourses } from './sidebar-courses.js';
import { initializeNotifications } from './notifications.js';

// Page initialization pattern
checkAuthAndRedirect().then(auth => {
  if (auth) {
    setupProfileDropdown();
    setupEnrollHoverPanel();
    loadSidebarCourses();
    initializeNotifications();
    // Page-specific initialization
  }
});
```

#### **Module export pattern:**
```javascript
// Export functions for reuse
export function setupProfileDropdown() {
  // Implementation
}

export async function loadNotifications() {
  // Implementation
}

// Default export за main functions
export default function initializePage() {
  // Main initialization logic
}
```

### **DOM Manipulation Patterns:**

#### **Element creation pattern:**
```javascript
function createCourseCard(course) {
  const card = document.createElement('div');
  card.className = 'course-card';
  card.innerHTML = `
    <div class="course-image">
      <img src="${course.image || 'images/default-course.jpg'}" alt="${course.name}">
    </div>
    <div class="course-content">
      <h3>${course.name}</h3>
      <p>${course.description}</p>
      <div class="course-meta">
        <span class="teacher">Преподавател: ${course.teacher}</span>
        <span class="period">${course.period}</span>
      </div>
    </div>
  `;

  // Add event listeners
  card.addEventListener('click', () => openCourse(course));

  return card;
}
```

#### **Dynamic content update pattern:**
```javascript
function updateCourseDisplay(courses) {
  const container = document.getElementById('coursesContainer');

  // Clear existing content
  container.innerHTML = '';

  if (courses.length === 0) {
    container.innerHTML = `
      <div class="no-courses">
        <i class="fas fa-book-open"></i>
        <p>Няма налични курсове</p>
      </div>
    `;
    return;
  }

  // Create course cards
  courses.forEach(course => {
    const courseCard = createCourseCard(course);
    container.appendChild(courseCard);
  });
}
```

### **Event Handling Strategies:**

#### **Delegated event handling:**
```javascript
// Event delegation за dynamic content
document.addEventListener('click', (e) => {
  if (e.target.matches('.course-card')) {
    const courseId = e.target.dataset.courseId;
    openCourse(courseId);
  }

  if (e.target.matches('.notification-item')) {
    const notificationId = e.target.dataset.notificationId;
    markAsRead(notificationId);
  }
});
```

#### **Form handling pattern:**
```javascript
function setupFormHandlers() {
  const forms = document.querySelectorAll('form[data-api-endpoint]');

  forms.forEach(form => {
    form.addEventListener('submit', async (e) => {
      e.preventDefault();

      const endpoint = form.dataset.apiEndpoint;
      const formData = new FormData(form);

      try {
        const res = await fetch(`http://localhost:5000${endpoint}`, {
          method: 'POST',
          credentials: 'include',
          body: formData
        });

        const data = await res.json();
        handleFormResponse(data, form);
      } catch (err) {
        showErrorMessage('Грешка при изпращане на формата');
      }
    });
  });
}
```

### **Form Validation Approaches:**

#### **Client-side validation:**
```javascript
function validateForm(form) {
  const errors = [];

  // Required field validation
  const requiredFields = form.querySelectorAll('[required]');
  requiredFields.forEach(field => {
    if (!field.value.trim()) {
      errors.push(`Полето "${field.placeholder || field.name}" е задължително`);
    }
  });

  // Email validation
  const emailFields = form.querySelectorAll('input[type="email"]');
  emailFields.forEach(field => {
    if (field.value && !isValidEmail(field.value)) {
      errors.push('Невалиден имейл адрес');
    }
  });

  // File validation
  const fileFields = form.querySelectorAll('input[type="file"]');
  fileFields.forEach(field => {
    if (field.files.length > 0) {
      const file = field.files[0];
      if (file.size > 10 * 1024 * 1024) { // 10MB
        errors.push('Файлът е твърде голям (максимум 10MB)');
      }
    }
  });

  return errors;
}

function isValidEmail(email) {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}
```

### **Error Handling на Frontend:**

#### **Global error handler:**
```javascript
// Global error handling
window.addEventListener('error', (e) => {
  console.error('Global error:', e.error);
  showErrorMessage('Възникна неочаквана грешка');
});

window.addEventListener('unhandledrejection', (e) => {
  console.error('Unhandled promise rejection:', e.reason);
  showErrorMessage('Грешка при обработка на заявката');
});
```

#### **API error handling pattern:**
```javascript
async function makeAPICall(endpoint, options = {}) {
  try {
    const res = await fetch(`http://localhost:5000${endpoint}`, {
      credentials: 'include',
      ...options
    });

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: ${res.statusText}`);
    }

    const data = await res.json();

    if (!data.success) {
      throw new Error(data.message || 'API call failed');
    }

    return data;
  } catch (err) {
    console.error(`API call failed for ${endpoint}:`, err);

    // Handle specific error types
    if (err.message.includes('401')) {
      // Unauthorized - redirect to login
      window.location.href = '/login.html';
    } else if (err.message.includes('403')) {
      // Forbidden - show access denied
      showErrorMessage('Нямате права за тази операция');
    } else if (err.message.includes('500')) {
      // Server error
      showErrorMessage('Сървърна грешка. Моля, опитайте отново');
    } else {
      // Generic error
      showErrorMessage(err.message || 'Възникна грешка');
    }

    throw err;
  }
}
```

### **Responsive Design Implementation:**

#### **Mobile-first CSS approach:**
```css
/* Base styles (mobile) */
.course-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1rem;
  padding: 1rem;
}

/* Tablet */
@media (min-width: 768px) {
  .course-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
    padding: 1.5rem;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .course-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    padding: 2rem;
  }
}

/* Large desktop */
@media (min-width: 1200px) {
  .course-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
```

#### **JavaScript responsive handling:**
```javascript
function handleResponsiveLayout() {
  const isMobile = window.innerWidth <= 768;
  const isTablet = window.innerWidth > 768 && window.innerWidth <= 1024;

  // Adjust layout based on screen size
  if (isMobile) {
    // Mobile-specific adjustments
    document.body.classList.add('mobile-layout');
    setupMobileNavigation();
  } else {
    document.body.classList.remove('mobile-layout');
    setupDesktopNavigation();
  }

  // Update grid columns
  const grid = document.querySelector('.course-grid');
  if (grid) {
    if (isMobile) {
      grid.style.gridTemplateColumns = '1fr';
    } else if (isTablet) {
      grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
    } else {
      grid.style.gridTemplateColumns = 'repeat(auto-fit, minmax(300px, 1fr))';
    }
  }
}

// Listen for resize events
window.addEventListener('resize', debounce(handleResponsiveLayout, 250));

// Debounce utility
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}
```

---

## 🎨 **ROLE-BASED UI РАЗЛИЧИЯ** {#role-based-различия}

### **Visual Design Differences:**

#### **Color Schemes:**
```css
/* Student Theme */
:root {
  --primary-color: #44749d;
  --secondary-color: #1abc9c;
  --accent-color: #e74c3c;
}

/* Teacher Theme */
:root {
  --teacher-accent: #9b59b6;
  --teacher-primary: #667eea;
  --teacher-secondary: #764ba2;
}
```

#### **Navigation Differences:**
| **Student Navigation** | **Teacher Navigation** |
|------------------------|------------------------|
| Начало | Начало |
| Моите Курсове | Всички курсове |
| Задачи | Учителско табло |
| Прогрес | Създай тест |
| Всички курсове | Моите планирани тестове |

### **Functional Differences:**

#### **Student-specific Features:**
- **Course Enrollment:** Hover panel за course codes
- **Task Management:** Urgent/Completed/Upcoming categories
- **Progress Tracking:** Personal statistics и charts
- **Test Taking:** Time-constrained test interface
- **File Submission:** Exercise и coursework uploads

#### **Teacher-specific Features:**
- **Course Creation:** Full course management
- **Student Management:** Grade assignment и progress monitoring
- **Test Creation:** Question builder с multiple types
- **Analytics Dashboard:** Course statistics и student performance
- **Content Management:** Upload lectures, exercises, coursework

### **Interface Complexity:**

#### **Student Interface - Simplified:**
- Focus на learning activities
- Streamlined navigation
- Clear task organization
- Progress visualization

#### **Teacher Interface - Advanced:**
- Comprehensive management tools
- Multiple data views
- Administrative functions
- Detailed analytics

---

## 🔗 **INTEGRATION С BACKEND** {#backend-integration}

### **Authentication Flow:**

#### **JWT Cookie Handling:**
```javascript
// Authentication check pattern
export async function checkAuthAndRedirect() {
  try {
    // Use existing endpoint to verify authentication
    const res = await fetch('http://localhost:5000/get-my-courses', {
      method: 'POST',
      credentials: 'include' // Include JWT cookie
    });

    const data = await res.json();

    if (!res.ok || !data.success) {
      // Handle token expiration
      if (data.message === '⏳ Токенът е изтекъл.') {
        console.warn('Сесията е изтекла.');
      }
      // Redirect to login
      window.location.href = '/login.html';
      return null;
    }

    return { isLoggedIn: true };
  } catch (err) {
    window.location.href = '/login.html';
    return null;
  }
}
```

#### **Login Process:**
```javascript
// Login form handling
async function handleLogin(email, password) {
  try {
    const res = await fetch('http://localhost:5000/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      credentials: 'include',
      body: JSON.stringify({ email, password })
    });

    const data = await res.json();

    if (data.success) {
      // JWT cookie is automatically set by server
      // Redirect based on user role
      if (data.user.role === 'student') {
        window.location.href = '/student/dashboard.html';
      } else if (data.user.role === 'teacher') {
        window.location.href = '/teacher/dashboard.html';
      }
    } else {
      showErrorMessage(data.message);
    }
  } catch (err) {
    showErrorMessage('Грешка при вход в системата');
  }
}
```

### **File Upload Integration с Cloudinary:**

#### **Frontend Upload Process:**
```javascript
async function uploadFile(file, endpoint, additionalData = {}) {
  // Show upload progress
  const progressBar = document.getElementById('uploadProgress');
  progressBar.style.display = 'block';

  const formData = new FormData();
  formData.append('file', file);

  // Add additional data
  Object.keys(additionalData).forEach(key => {
    formData.append(key, additionalData[key]);
  });

  try {
    const res = await fetch(`http://localhost:5000${endpoint}`, {
      method: 'POST',
      credentials: 'include',
      body: formData
    });

    const data = await res.json();

    if (data.success) {
      // File uploaded to Cloudinary successfully
      showSuccessMessage('Файлът е качен успешно!');

      // Update UI with Cloudinary URL
      updateFileDisplay({
        url: data.url,
        originalName: data.originalName,
        publicId: data.publicId
      });
    } else {
      showErrorMessage(data.message);
    }
  } catch (err) {
    showErrorMessage('Грешка при качване на файла');
  } finally {
    progressBar.style.display = 'none';
  }
}
```

#### **Backend Integration Flow:**
```
Frontend File Upload Flow:
1. User selects file → File validation (client-side)
2. FormData creation → POST to server endpoint
3. Server receives → Multer processes → Streamifier converts
4. Cloudinary upload → URL returned → MongoDB reference stored
5. Success response → Frontend updates UI → File accessible via CDN
```

### **Real-time Data Updates:**

#### **Notification Polling:**
```javascript
// Real-time notification updates
function initializeNotifications() {
  setupNotificationBell();
  loadNotifications();

  // Poll every 30 seconds
  setInterval(loadNotifications, 30000);
}

async function loadNotifications() {
  try {
    const res = await fetch('http://localhost:5000/get-notifications', {
      method: 'POST',
      credentials: 'include'
    });

    const data = await res.json();
    if (data.success) {
      notifications = data.notifications || [];
      updateNotificationDisplay();

      // Check for urgent notifications
      const urgentNotifications = notifications.filter(n => n.isUrgent && !n.isRead);
      if (urgentNotifications.length > 0) {
        showUrgentNotificationAlert(urgentNotifications);
      }
    }
  } catch (err) {
    console.error('Error loading notifications:', err);
  }
}
```

#### **Progress Data Synchronization:**
```javascript
// Real-time progress updates
async function loadStudentProgress() {
  try {
    const res = await fetch('http://localhost:5000/get-student-progress', {
      method: 'POST',
      credentials: 'include'
    });

    const data = await res.json();
    if (data.success) {
      updateProgressCharts(data.progressData);
      updateStatistics(data.progressData);

      // Cache data for offline viewing
      localStorage.setItem('studentProgress', JSON.stringify(data.progressData));
    }
  } catch (err) {
    // Fallback to cached data
    const cachedData = localStorage.getItem('studentProgress');
    if (cachedData) {
      const progressData = JSON.parse(cachedData);
      updateProgressCharts(progressData);
      showOfflineMessage();
    }
  }
}
```

### **API Endpoint Mapping:**

#### **Student Frontend → Backend Mapping:**
```javascript
// Student API calls mapping
const STUDENT_ENDPOINTS = {
  // Course management
  enrollCourse: '/enroll-course',
  getMyCourses: '/get-my-courses',
  getCourseTests: '/get-course-tests',

  // File uploads
  uploadExercise: '/upload-exercise',
  uploadCoursework: '/upload-coursework',
  getAttachments: '/get-attachments',

  // Test system
  startTestSession: '/start-test-session',
  submitTest: '/submit-test',
  getTestTimeRemaining: '/get-test-time-remaining',

  // Progress tracking
  getStudentProgress: '/get-student-progress',
  getStudentNotifications: '/get-student-notifications',

  // Notifications
  markNotificationRead: '/mark-notification-read',
  markAllNotificationsRead: '/mark-all-notifications-read'
};
```

#### **Teacher Frontend → Backend Mapping:**
```javascript
// Teacher API calls mapping
const TEACHER_ENDPOINTS = {
  // Course management
  createCourse: '/create-course',
  getAllCourses: '/get-all-courses',
  addCourseContent: '/add-course-content',
  updateCourseContent: '/update-course-content',
  deleteCourseContent: '/delete-course-content',

  // Student management
  getCourseStudents: '/get-course-students',
  getStudentSubmissions: '/get-student-submissions',
  saveGrade: '/save-grade',
  updateGrade: '/update-grade',
  getStudentAverage: '/get-student-average',

  // Test management
  createTest: '/create-test',
  getTeacherTests: '/get-teacher-tests',
  updateTest: '/update-test',
  deleteTest: '/delete-test',

  // Analytics
  getTestResultsSummary: '/get-test-results-summary',
  getTestResultsDetailed: '/get-test-results-detailed',
  exportTestResults: '/export-test-results'
};
```

### **Error Handling Integration:**

#### **Server Error Response Handling:**
```javascript
function handleServerResponse(data, context = '') {
  if (data.success) {
    // Success handling
    if (data.message) {
      showSuccessMessage(data.message);
    }
    return data;
  } else {
    // Error handling based on server response
    const errorMessage = data.message || 'Възникна грешка';

    // Handle specific error types
    if (errorMessage.includes('Токенът е изтекъл')) {
      // Session expired
      showErrorMessage('Сесията е изтекла. Моля, влезте отново.');
      setTimeout(() => {
        window.location.href = '/login.html';
      }, 2000);
    } else if (errorMessage.includes('Access denied')) {
      // Permission denied
      showErrorMessage('Нямате права за тази операция');
    } else {
      // Generic error
      showErrorMessage(errorMessage);
    }

    throw new Error(errorMessage);
  }
}
```

---

## 📋 **ЗАКЛЮЧЕНИЕ**

### **Frontend Architecture Highlights:**

#### **Strengths:**
- ✅ **Complete Role Separation** - Пълно разделение между student/teacher интерфейси
- ✅ **Modular ES6+ Architecture** - Reusable modules и clean imports
- ✅ **Responsive Design** - Mobile-first approach с comprehensive breakpoints
- ✅ **Real-time Features** - Notification система с 30-second polling
- ✅ **Robust Error Handling** - Multi-level error management
- ✅ **Consistent API Integration** - Standardized patterns за backend communication

#### **Technical Achievements:**
- **JWT Cookie Authentication** - Secure, stateless authentication
- **Cloudinary Integration** - Professional file management workflow
- **Progressive Enhancement** - Graceful degradation за offline scenarios
- **Performance Optimization** - Efficient DOM manipulation и caching
- **Accessibility** - Semantic HTML и keyboard navigation support

#### **Scalability Features:**
- **Component-based Architecture** - Easy to extend и maintain
- **API Abstraction** - Clean separation между UI и data layers
- **Responsive Framework** - Adapts to different screen sizes
- **Error Recovery** - Robust handling на network issues

### **Production Readiness:**
Тази frontend архитектура представлява **professional-grade solution** с modern development practices, comprehensive user experience, и scalable design patterns подходящи за educational technology platform в production environment.

---

*Тази comprehensive документация покрива всички аспекти на frontend архитектурата на студентската система за управление, организирана по роли и технически детайли за академично ниво.*
